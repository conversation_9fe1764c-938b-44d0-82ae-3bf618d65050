<?php

    /**
     * 登录
     **/
    $verifycode   = 1; //验证码开关
    $cloudifycode = 1; //短信验证码开关
    $emsifycode   = 0; //邮箱验证码开关
    include "../includes/common.php";

    $adm_email = conf('adm_email');
    if (conf('adm_login_checkemail') == 1 && \core\Ems::checkIsRun() && validateData($adm_email, 'email')) {
        $emsifycode = 1;
    }

    if (!function_exists("imagecreate") || !file_exists('code.php') || session_status() < 2) {
        $verifycode = 0;
    }

    // $emsifycode = 0;

    $act = isset($_GET['act']) ? $_GET['act'] : null;
    if ($act == 'login') {
        $user               = input('post.username', 1);
        $pass               = input('post.password', 1);
        $vcode              = input('post.vcode', 1);
        $api                = isset($_POST['api']) ? input('post.api', 1) : null;
        $admin_login_errnum = isset($_SESSION['admin_login_errnum']) ? intval($_SESSION['admin_login_errnum']) : 0;
        if ($admin_login_errnum >= 10 && $_SESSION['admin_login_errtime'] > time()) {
            $result = ['code' => -1, 'msg' => "您账号密码错误次数已达到10次，请稍后再试"];
        } elseif ($verifycode == 1 && (!$vcode || !captcha_check($vcode, 'admin'))) {
            $result = ['code' => -3, 'msg' => "图形验证码错误！"];
        } elseif ($user === $conf['adm_user'] && ($pass === $conf['adm_pwd'] || $conf['adm_pwd'] === sha1(md5($pass . $conf['syskey']) . 'cmy 11111111'))) {
            if ($cloudifycode == 1 && checkMessage('yidilogin', 1) && $conf['adm_tel'] != "" && $conf['loginIp'] != $clientip) {
                $code   = input('post.code', 1);
                $userid = md5($cookiesid . ' 11111111' . $code);
                if (empty($code)) {
                    $result = ['code' => -2, 'msg' => '验证码不能为空！'];
                } elseif (empty($_SESSION['code_userid'])) {
                    $result = ['code' => -2, 'msg' => '异地登录，请先发送验证码！'];
                } else {
                    $logrow = $DB->get_row("select * from cmy_codelog where `tel`='" . $conf['adm_tel'] . "' and `code`='" . $code . "' limit 1");
                    if (!$logrow) {
                        $result = ['code' => -1, 'msg' => '该验证码不正确！'];
                    } else {
                        if ($logrow['status'] == 1) {
                            $result = ['code' => -1, 'msg' => '该验证码已失效，请重新发送！'];
                        } elseif (time() - strtotime($logrow['addtime']) > 600) {
                            $result = ['code' => -1, 'msg' => '该验证码已过期！'];
                        } else {
                            $DB->query("update `pre_codelog` set `status`='1' where id='" . $logrow['id'] . "' limit 1");
                            unset($_SESSION['code_userid']);
                        }
                    }
                }

                if (isset($result) && is_array($result)) {
                    exit(json_encode($result));
                }
            }

            // 系统邮箱
            if ($emsifycode == 1 && conf('adm_login_checkemail') == 1 && validateData($adm_email, 'email')) {
                $code2 = input('code2');
                $event = 'login';
                try {
                    $ems   = new \core\Ems();
                    $check = $ems->check($adm_email, $code2 ?: null, $event);
                    if ($check !== true) {
                        json($check, 406);
                    }
                } catch (\Exception $th) {

                }
            }

            $_SESSION['admin_login_errnum'] = 0;
            unset($_SESSION['vc_code']);
            $session = md5($user . $conf['adm_pwd'] . $password_hash);
            $token   = authcode("{$user}\t{$session}", 'ENCODE', SYS_KEY);

            // 登陆过期时间
            $expire = $conf['adm_login_expire'] > 0 ? $conf['adm_login_expire'] * 3600 : 48 * 3600;
            if (setcookie("admin_token", $token, time() + $expire)) {
                try {
                    saveSetting('admin_logintime', $date);
                    saveSetting('admin_loginip', x_real_ip());
                    if ($api > 0) {
                        setAuthServer($api);
                    }

                    if (\core\Ems::checkIsRun() && validateData($adm_email, 'email')) {
                        // 登录提醒
                        try {
                            $ems = new \core\Ems();
                            $ems->sendEmail($adm_email, '安全提醒！您已成功登录后台', '尊敬的管理员<b>' . $user . '</b>, 您已成功登录后台!<br/>如非本人操作, 你的账号和密码可能已经泄露, 请及时核查<br/>登录IP地址 :' . $clientip . '<br/>登录IP城市:' . getIpAddress());
                        } catch (\Exception $th) {
                            //throw $th;
                        }
                    }

                    $CACHE->clear();
                    fzlog_result(1, '后台登录', '用户名：' . $user . '；登录IP：' . $clientip, '登录成功', 1);
                    $result = ['code' => 0, 'msg' => "登陆管理中心成功！", 'token' => $_COOKIE["admin_token"]];
                } catch (\PDOException $e) {
                    $result = ['code' => -1, 'msg' => "登陆失败，" . $e->getMessage()];
                }
            } else {
                $result = ['code' => -1, 'msg' => "登录失败，客户端时间与北京时间不一致或浏览器环境不支持！"];
            }
        } else {
            $row = $DB->get_row("SELECT * from cmy_admin where user=:user limit 1", [':user' => $user]);
            if ($row && $row['pwd'] === $pass) {
                if ($row['status'] != 1) {
                    $result = ['code' => -1, 'msg' => "该管理员账户已被关闭！", 'user' => $conf['adm_user']];
                } else {
                    unset($_SESSION['vc_code']);
                    $session = md5($user . $row['pwd'] . $password_hash);
                    $token   = authcode("{$row['aid']}\t{$session}", 'ENCODE', SYS_KEY);
                    $expire  = $conf['adm_login_expire'] > 0 ? $conf['adm_login_expire'] * 86400 : 3 * 86400;
                    setcookie("account_token", $token, time() + $expire);
                    saveSetting('account_' . $account['uid'] . '_loginip', x_real_ip());
                    $CACHE->clear();
                    $sqlData = [':date' => $date, ':ip' => $clientip, ':aid' => $row['aid']];
                    $DB->query("UPDATE `pre_admin` SET `lasttime` = :date,`ip` = :clientip WHERE aid=:aid", $sqlData);
                    fzlog_result(1, '管理员登录', '用户名：' . $user . '；登录IP：' . $clientip, '登录成功', 1);
                    $result = ['code' => 0, 'msg' => "欢迎回来，管理员！"];
                }
            } else {
                unset($_SESSION['vc_code']);
                $_SESSION['admin_login_errnum']  = intval($_SESSION['admin_login_errnum']) + 1;
                $_SESSION['admin_login_errtime'] = time() + 1800;
                fzlog_result(1, '管理员登录', '用户名：' . $user . '；登录IP：' . $clientip, '管理员用户名或密码不正确！', 0);
                $result = ['code' => -1, 'msg' => "用户名或密码不正确！"];
            }
        }
        exit(json_encode($result));
    } elseif ($act == 'sendCode') {
        $tel    = daddslashes($conf['adm_tel']);
        $type   = intval($_GET['type']);
        $result = sendCode($tel, $type);
        exit(json_encode($result));
    } elseif (isset($_GET['logout'])) {
        if (isset($_COOKIE["admin_token"])) {
            setcookie("admin_token", "", time() - 604800);
        }
        if (isset($_COOKIE["account_token"])) {
            setcookie("account_token", "", time() - 604800);
        }
        @header('Content-Type: text/html; charset=UTF-8');
        exit("<script language='javascript'>alert('安全提醒！您已成功注销本次登陆！');window.location.href='./login.php';</script>");
    }

    $select = null;

    $title = '管理后台登录';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><?php echo $title; ?>-<?php echo $conf['sitename']; ?></title>
  <meta name="description" content="<?php echo $conf['sitename']; ?>,用户登录">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link crossorigin="anonymous" href="<?php echo $cdnpublic; ?>animate.css/3.7.2/animate.min.css" rel="stylesheet">
  <link href="<?php echo $cdnpublic; ?>twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="<?php echo $cdnpublic; ?>font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="<?php echo $cdnserver; ?>assets/simple/css/oneui.css">
  <link rel="stylesheet" href="<?php echo $cdnserver; ?>assets/user/css/app.css" type="text/css" />
  <style type="text/css">
    #admin-body{
        background-color: #e2e2e2;
    }
    #admin-content{
        padding-top: 42px;
        margin: 0 auto;
    }
    #admin-title{
        color: dodgerblue;
    }
    .block{z-index: 2;border-radius: 15px;background: rgba(255, 255, 255, 0.4);margin: 20px auto;transition: all 1s;-moz-transition: all 1s;    /* Firefox 4 */-webkit-transition: all 1s;  /* Safari 和 Chrome */-o-transition: all 1s; /* Opera */-moz-box-shadow:0px 2px 5px #333333; -webkit-box-shadow:0px 2px 5px #333333; box-shadow:0px 2px 5px #333333;}
    .login-top{text-align: center;font-size: 18px;box-sizing: border-box;color: #fff;padding: 10px;}
    .login-icon{font-size: 20px;color: dodgerblue;}
    .form-control{padding: 20px 12px;}
    input.form-control:focus{border: 1px solid dodgerblue;}
    input.form-control:blur{border: 0 solid dodgerblue;}
    .input-sendcode{
        font-size: 14px;
        font-weight: 400;
        line-height: 0;
        color: #555;
        text-align: center;
        background-color: #eee;
        border: 1px solid #ccc;
        height: 41px;
        width: 1%;
        white-space: nowrap;
        vertical-align: middle;
        display: table-cell;
        box-sizing: content-box;
    }
    .cmy-bg{
        position: absolute;
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        top: 0;
    }
  </style>
</head>
<body id="body-admin">
<img src="../assets/img/dlbj.png" alt="Full Background" class="cmy-bg">
<div id="admin-content" class="center-block col-xs-10 col-sm-10 col-md-8 col-lg-3" style="float: none;padding-left: 5px;padding-right: 5px">
        <h1 id="admin-title" class="h2 text-light text-center push-top-bottom animation-slideDown">
            <i class="fa fa-cube"></i>&nbsp;<?php echo $conf['sitename']; ?>
        </h1>
        <div class="block" style="max-width: 420px;">
            <!-- <div class="widget-content themed-background-flat text-center animated bounceInDown"  style="padding: 10px 0;" >
               <img  class="img-circle"src="//q4.qlogo.cn/headimg_dl?dst_uin=<?php echo $conf['zzqq'] ? $conf['zzqq'] : ' 11111111'; ?>&spec=100" alt="Avatar" alt="avatar" height="60" width="60" />
            </div> -->
            <div class="panel-body">
                <form id="myForm">
                    <div class="login-top">
                       <?php echo $title; ?>
                    </div>
                    <div class="form-group">
                        <div class="input-group"><div class="input-group-addon fa fa-user login-icon"></div>
                        <input type="text" name="username" id="user" onkeydown="if(event.keyCode==13){$('#pass').focus()}" class="form-control" value="" placeholder="请输入您的后台用户名"/>
                    </div></div>
                    <div class="form-group">
                        <div class="input-group"><div class="input-group-addon fa fa-lock login-icon"></div>
                        <input type="password" name="password" id="pass" onkeydown="if(event.keyCode==13){$('#login').click()}" value="" class="form-control" placeholder="请输入您的后台密码"/>
                    </div></div>
                    <?php if ($verifycode == 1) {?>
                    <div class="form-group" id="display_vcode">
                        <div class="input-group"><div class="input-group-addon fa fa-lock login-icon"></div>
                        <input type="text" value="" name="vcode" id="vcode" onkeydown="if(event.keyCode==13){$('#login').click()}" class="form-control" placeholder="请输入文字验证码"/>
                        <div class="input-group-addon" style="padding: 0;width: 108px;border: none;">
                            <img id="codeimg" style="width: 100%;" src="./code.php?r=<?php echo time(); ?>" onclick="this.src='./code.php?r='+Math.random();" title="点击更换文字验证码">
                        </div>
                    </div></div>
                    <?php }?>
                    <div class="form-group" id="display_sms" style="display: none;">
                        <div class="input-group"><div class="input-group-addon fa fa-check-square login-icon"></div>
                        <input type="text" id="code" name="code" value="" onkeydown="if(event.keyCode==13){$('#login').click()}" class="form-control" placeholder="请输入短信验证码"/>
                        <div class="input-group-addon sendCode" data-type="sms">
                            发送验证码
                        </div>
                    </div></div>
                    <div class="form-group" id="display_email" style="display:<?php echo $emsifycode == 1 ? 'initial' : 'none' ?>;">
                        <div class="input-group"><div class="input-group-addon fa fa-check-square login-icon"></div>
                        <input type="text" id="code2" name="code2" value="" onkeydown="if(event.keyCode==13){$('#login').click()}" class="form-control" placeholder="请输入邮箱验证码"/>
                        <div class="input-group-addon sendCode" data-type="ems">
                            发送验证码
                        </div>
                    </div></div>
                    <div class="login-btn animated bounceInUp">
                        <a id="login" class="btn btn-primary btn-block">登录后台</a>
                    </div>
                    <br/>
                    <div class="form-group">
                        <div class="input-group">
                            <div class="input-group-addon">选择节点</div>
                            <select name="api" id="api" class="form-control" style="padding: 5px 5px;">
                                <option value="-1">节点获取中，请稍后~</option>
                            </select>
                            <a class="input-group-addon api-check">
                            连通测试
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <footer class="text-muted text-center animation-pullUp" style="color: #fff">
        <small>2018~<?php echo date('Y'); ?> &copy; <a href="#"><?php echo $conf['sitename']; ?></a>&nbsp;Powered by <a href="./" target="_blank">斑马</a></small>
        </footer>
        <div class="sk-rotating-plane"></div>
</div>

<!-- scripts -->

<script src="<?php echo $cdnpublic; ?>jquery/3.1.1/jquery.min.js"></script>
<script src="<?php echo $cdnpublic; ?>layer/3.4.0/layer.js?<?php echo $jsver; ?>"></script>
<script type="text/javascript">
$("#login").click(function (){
    var user=$("#user").val();
    var pass=$("#pass").val();
    var vcode=$("#vcode").val();
    var code=$("#code").val();
    if (""==user) {
        return layer.alert("后台用户名不能为空！");
    }
    else if (""==pass) {
        return layer.alert("后台密码不能为空！");
    }
    var ii = layer.load(2, {shade:[0.1,'#fff']});
    $.ajax({
        type : "POST",
        url : "?act=login",
        data : $("#myForm").serialize(),
        dataType : 'json',
        success : function(data) {
            layer.close(ii);
            if(data.code == 0){
                layer.msg("登录成功，正在跳转..");
                setTimeout(function (){
                    <?php if (isset($_GET['goto']) && !empty($_GET['goto'])): ?>
                    window.location.href='<?php echo $_GET['goto']; ?>';
                    <?php else: ?>
                    window.location.href="./list.php";
                    <?php endif; ?>
                }, 1000);
            }
            else if(data.code == -3){
                layer.msg(data.msg);
                $("#codeimg").click();
            }
            else if(data.code == -2){
                layer.msg(data.msg);
                $("#display_code").show();
            }
            else if(data.code == 406){
                // 邮件验证
                layer.msg(data.msg);
                $("#display_email").show();
                $("#display_sms").hide();
            }
            else if(data.code == 407){
                // 短信验证
                layer.msg(data.msg);
                $("#display_sms").show();
                $("#display_email").hide();
            }
            else{
                layer.alert(data.msg);
            }
        },
        error:function(){
            layer.close(ii);
            layer.alert('服务器请求错误');
        }
    });
});

$(document).on('click', '.api-check', function(event) {
    event.preventDefault();
    /* Act on the event */
    var ii = layer.load(2, {
        shade: [0.1, "#fff"]
    });
    $.ajax({
        type: "POST",
        url: "./ajax.public.php?act=api_check",
        data: {
            id : $("#api").val()
        },
        dataType: "json",
        success: function(data) {
            layer.closeAll();
            layer.msg(data.msg);
        },
        error:function(err){
            layer.closeAll();
            layer.msg('连接超时，请换一个节点再试！');
        }
    });
});

$(document).on('click', '.sendCode', function () {
    var ii = layer.load(2, {shade:[0.1,"#fff"]});
    var type = $(this).data('type');
    $.ajax({
        type : "POST",
        url : "ajax.public.php?act=" + (type == 'sms'?'sms_send':'ems_send'),
        data: {
            event:"login"
        },
        dataType : "json",
        success : function(data) {
            layer.close(ii);
            // 每次请求验证码都会变
            $("#codeimg").click();
            if(data.code == 0){
                layer.msg(data.msg);
                $(".sendCode").html("已发送");
                // $("#display_vcode").hide();
                setTimeout(function(){
                    $(".sendCode").html("发送验证码");
                }, 1500);
            }else{
                layer.alert(data.msg);
            }
        }
    });
});


function getServerList() {
    $.ajax({
      type : "POST",
      url : "./ajax.public.php?act=getServerList",
      dataType : "json",
      success : function(data) {
        if(data.code == 0){
            $("#api").empty().append(data.data)
        }else{
          layer.alert(data.msg);
        }
      }
    });
}

$("input").focus(function (){
    $(this).parents('.login-center .clearfix').find('.login-center-input-text').addClass('active');
});

$("input").blur(function (){
    $(this).parents('.login-center .clearfix').find('.login-center-input-text').removeClass('active');
});

window.onload = function () {
    getServerList();
}
</script>
</body>
</html>