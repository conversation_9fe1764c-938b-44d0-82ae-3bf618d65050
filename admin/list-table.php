<?php
/**
 * 订单列表
 * 斑马
 *
 **/

include "../includes/common.php";

checkLogin();

function display_zt($zt, $id = 0)
{
    if ($zt == 1) {
        return '<font color=green>已完成</font>';
    } elseif ($zt == 2) {
        return '<font color=orange>正在处理</font>';
    } elseif ($zt == 3) {
        return '<font color=red>异常</font>';
    } elseif ($zt == 4) {
        return '<font color=grey>已退款</font>';
    } elseif ($zt == 10) {
        return '<font color=#8E9013>待退款</font>';
    } elseif ($zt == -1) {
        return '<font color=#9E9E9E>创建中</font>';
    } else {
        return '<font color=blue>待处理</font>';
    }
}

function getOrderPayType($row)
{

    if ($row['type'] == 'wxpay') {
        $type = '微信';
    } elseif ($row['type'] == 'alipay') {
        $type = '支付宝';
    } elseif ($row['type'] == 'qqpay') {
        $type = 'QQ钱包';
    } elseif ($row['type'] == 'rmb') {
        $type = '余额支付';
    } elseif ($row['type'] == 'free') { 
        $type = '免费<i title="该订单是免费商品创建的" class="fa fa-question-circle"></i>';
    } elseif ($row['type'] == 'kj_free') {
        $type = '砍价<i title="该订单是通过砍价并完成后创建的" class="fa fa-question-circle"></i>';
    } elseif ($row['type'] == 'gift_free') {
        $type = '抽奖<i title="该订单是通过抽奖并中奖后创建的" class="fa fa-question-circle"></i>';
    } elseif ($row['type'] == 'rmb_alipay') {
        $type = '在线支付宝<i title="该用户/分站登陆后使用支付宝支付" class="fa fa-question-circle"></i>';
    } elseif ($row['type'] == 'rmb_wxpay') {
        $type = '在线微信<i title="该用户/分站登陆后使用微信支付" class="fa fa-question-circle"></i>';
    } elseif ($row['type'] == 'rmb_qqpay') {
        $type = '在线QQ钱包<i title="该用户/分站登陆后使用QQ钱包支付" class="fa fa-question-circle"></i>';
    } elseif ($row['type'] == 'move') {
        $type = '<span>未知</span><i title="该订单是其他系统转移过来的未知支付方式，请忽略即可" class="fa fa-question-circle"></i>';
    } else {
        $type = '<span style="color:red">类型异常</span><i title="该支付类型不合法，请检查订单是否异常" class="fa fa-question-circle"></i>';
    }

    return $type;
}

function getOrderInput($row)
{

    $input = '<span>' . $row['input'] . '<a onclick="inputOrder(' . $row['id'] . ')" data-tip="点击可修改下单数据、份数等" title="点击修改数据"><i class="fa fa-pencil-square-o fa-fw" style="    margin-left: 4px; color: #E91E63;"></i></a>';
    if (!empty($row['input2'])) {
        $input .= '<hr class="hr-black"/>' . $row['input2'];
    }

    if (!empty($row['input3'])) {
        $input .= '<hr class="hr-black"/>' . $row['input3'];
    }

    if (!empty($row['input4'])) {
        $input .= '<hr class="hr-black"/>' . $row['input4'];
    }

    if (!empty($row['input5'])) {
        $input .= '<hr class="hr-black"/>' . $row['input5'];
    }

    if (!empty($row['inputattr'])) {
        $input .= '<hr class="hr-black"/>属性：' . $row['inputattr'];
    }

    if (!empty($row['bz'])) {
        $input .= '<hr class="hr-black"/>备注：' . $row['bz'];
    }

    $input .= '</span>';

    return $input;
}

function getMoneyColor($money)
{
    if ($money == 0) {
        return '#27c24c';
    } elseif ($money >= 101) {
        return '#000000';
    } elseif ($money >= 31) {
        return '#EE33EE';
    } elseif ($money >= 11) {
        return '#ff9224';
    } elseif ($money >= 1) {
        return '#E53333';
    } else {
        return 'rgb(88, 102, 110)';
    }
}

function getNumColor($num)
{
    if ($num >= 10) {
        return '#000000';
    } elseif ($num >= 6) {
        return '#EE33EE';
    } elseif ($num >= 3) {
        return '#ff9224';
    } elseif ($num >= 2) {
        return '#E53333';
    } else {
        return '#27c24c';
    }
}

function display_djzt($djzt, $id, $zt, $djorder = "")
{

    if ($djzt == 1) {
        if ($zt == 0 && $djorder == "") {
            return '<span onclick="djOrder(' . $id . ')" title="点击重试" class="btn btn-danger btn-xs">失败</span>';
        } else {
            return '<span onclick="showStatus(' . $id . ')" title="查看订单进度" class="btn btn-success btn-xs">成功</span>';
        }

    } elseif ($djzt == 2) {
        return '<span onclick="djOrder(' . $id . ')" title="点击重试" class="btn btn-danger btn-xs">失败</span>';
    } elseif ($djzt == 3) {
        return '<a onclick="window.open(\'fakakms.php?orderid=' . $id . '\')" title="查看卡密信息"><font color=green>已发货</font></a>';
    } elseif ($djzt == 4) {
        return '<span onclick="djOrder(' . $id . ')" title="点击重试" class="btn btn-danger btn-xs">未发货</span>';
    } elseif ($djzt == 5) {
        return '<a onclick="return 1;"><font color=green>访问成功</font></a>';
    } elseif ($djzt == 6) {
        return '<span onclick="djOrder(' . $id . ')" title="点击重试" class="btn btn-danger btn-xs">未访问</span>';
    } else {
        return '<font color=grey>未对接</font>';
    }

}

$rs        = $DB->query("SELECT * FROM cmy_tools WHERE 1 order by sort asc");
$select    = '';
$data      = [];
$cmy_func  = [];
$cmy_tools = [];
if ($rs) {
    $data = $DB->fetchAll($rs);
    foreach ($data as $res) {
        $cmy_func[$res['tid']]  = $res['name'];
        $cmy_tools[$res['tid']] = $res;
        $select .= '<option value="' . $res['tid'] . '">' . $res['name'] . '</option>';
    }
}

if (isset($_GET['kw']) && !empty($_GET['kw'])) {

    $kw     = input('get.kw', 1);
    $status = intval(input('get.status', 1));
    $type   = intval(input('get.type', 1));
    if ($type == 4) {
        $sql  = " (`input`='" . $kw . "' or `input2`='" . $kw . "' or `input3`='" . $kw . "' or `input4`='" . $kw . "' or `input5`='" . $kw . "')";
        $type = '下单数据';
    } elseif ($type == 1) {
        $sql  = " `id`='{$kw}'";
        $type = '订单编号';
    } elseif ($type == 2) {
        $sql      = " `name` like '%{$kw}%'";
        $cs       = $DB->query("SELECT * from cmy_tools WHERE{$sql}");
        $tid_list = '';
        while ($rcs = $DB->fetch($cs)) {
            $tid_list .= $rcs['tid'] . ",";
        }
        $tid_list = trim($tid_list, ',');
        $sql      = " `tid` in (" . $tid_list . ")";
        $type     = '商品名称';

    } elseif ($type == 3) {
        $sql  = " `tid`='{$kw}'";
        $type = '商品编号';
    } elseif ($type == 5) {
        $sql  = " `zid`='{$kw}'";
        $type = '站点ID';
    } else {
        $sql  = " (`input`='" . $kw . "' or `input2`='" . $kw . "' or `input3`='" . $kw . "' or `input4`='" . $kw . "' or `input5`='" . $kw . "' or `id`='{$kw}' or `zid`='{$kw}' or `payorder`='{$kw}')";
        $type = '自动匹配';
    }

    if ($status >= 0) {
        $sql .= " and status=" . $status;
        $con = "且&nbsp;&nbsp;" . display_zt($status) . '&nbsp;&nbsp;';
    }
    $numrows = (int) $DB->count("SELECT count(*) from cmy_orders WHERE{$sql}");
    $con     = '包含&nbsp;&nbsp;&nbsp;<font color=green>' . $type . '：</font>&nbsp;&nbsp;<font color=red>' . $kw . '</font>&nbsp;&nbsp;' . $con . '的共有 <b>' . $numrows . '</b> 个订单';
    $link    = "&kw=" . $kw . "&status=" . $status . "&type=" . intval($_GET['type']);

} elseif (isset($_GET['id'])) {

    $id  = intval(input('get.id', 1));
    $sql = " `id`='{$id}'";

    $numrows = $DB->count("SELECT count(*) from cmy_orders WHERE{$sql}");

    $con = '';

    $link = '&id=' . $id;

} elseif (isset($_GET['tid'])) {

    $tid = intval(input('get.tid', 1));
    $sql = " `tid`='{$tid}'";

    $numrows = $DB->count("SELECT count(*) from cmy_orders WHERE{$sql}");

    $con = $cmy_func[$tid] . ' 共有 <b>' . $numrows . '</b> 个订单';

    $link = '&tid=' . $tid;

} elseif (isset($_GET['zid'])) {
    $zid = intval(input('get.zid', 1));

    $sql = " `zid`='{$zid}'";

    $numrows = $DB->count("SELECT count(*) from cmy_orders WHERE{$sql}");

    $con = '站点ID ' . $_GET['zid'] . ' 共有 <b>' . $numrows . '</b> 个订单';

    $link = '&zid=' . $zid;

} elseif (isset($_GET['uid'])) {
    $uid = intval(input('get.uid', 1));
    $sql = " `userid`='{$uid}'";

    $numrows = $DB->count("SELECT count(*) from cmy_orders WHERE{$sql}");

    $con = '用户ID ' . $uid . ' 共有 <b>' . $numrows . '</b> 个订单';

    $link = '&uid=' . $uid;

} elseif (isset($_GET['status']) && $_GET['status'] >= 0) {
    $status = intval(input('get.status', 1));
    $sql    = " `status`='{$status}'";

    $numrows = $DB->count("SELECT count(*) from cmy_orders WHERE{$sql}");

    $con = '' . display_zt($status) . ' 状态的共有 <b>' . $numrows . '</b> 个订单';

    if ($_GET['type'] == 3) {
        $con .= '&nbsp;[<a href="list.php?my=fillall" onclick="return confirm(\'你确定要将所有异常订单改为待处理状态吗？\');">将所有异常订单改为待处理状态</a>]';
    }

    $link = "&status=" . $status;

} else {

    $numrows = $DB->count("SELECT count(*) from cmy_orders");

    $ondate  = $DB->count("select count(*) from cmy_orders where status=1");

    $ondate2 = $DB->count("select count(*) from cmy_orders where status=2");

    $ondate3 = $DB->count("select count(*) from cmy_orders where status=0");
    
    $ondate4 = $DB->count("select count(*) from cmy_orders where status=3");
    
    $ondate5 = $DB->count("select count(*) from cmy_orders where status=10");
    
    $ondate6 = $DB->count("select count(*) from cmy_orders where status=4");

    $sql = " 1";

    $con = '系统共有 <b>' . $numrows . '</b> 个订单，其中已完成的有 <b>' . $ondate . '</b> 个，正在处理的有 <b><a href="./list.php?status=2" style="color: #0000FF;">' . $ondate2 . '</a></b> 个，待处理的有 <b><a href="./list.php?status=0" style="color: #dc3545;">' . $ondate3 . '</a></b> 个，异常的有 <b><a href="./list.php?status=3" style="color: #dc3545;">' . $ondate4 . '</a></b> 个，待退款的有 <b><a href="./list.php?status=10" style="color: #0000FF;">' . $ondate5 . '</a></b> 个，已退款的有 <b><a href="./list.php?status=4">' . $ondate6 . '</a></b> 个。';

}

echo '<form name="form1" id="form1">

      <div class="table-responsive">
      ';

echo $con;

echo '
<table class="table table-striped table-bordered table-vcenter">

          <thead><tr><th>订单ID</th><th width="85px">供货商</th><th style="max-width: 260px;">商品名称</th><th style="max-width: 240px;">下单数据</th><th style="width: 100px;">付款方式</th><th style="width: 80px;">金额</th><th style="width: 80px;">份数</th><th style="width: 80px;">站点ID</th><th style="width:185px;">添加/更新时间</th><th style="width: 75px;">对接状态</th><th style="width: 75px;">订单状态</th><th style="width:115px;">操作</th></tr></thead>

          <tbody>
';

$pagesize = $conf['index_pagesize'] ? $conf['index_pagesize'] : 30;

$pages = ceil($numrows / $pagesize);

$page = isset($_GET['page']) ? intval($_GET['page']) : 1;

$offset = $pagesize * ($page - 1);

if ($numrows > 0) {
    $rs = $DB->query("SELECT * FROM cmy_orders WHERE{$sql} order by id desc limit $offset,$pagesize");

    while ($res = $DB->fetch($rs)) {
        $tool = isset($cmy_tools[$res['tid']]) ? $cmy_tools[$res['tid']] : null;
        echo '<tr><td><input type="checkbox" name="checkbox[]" id="list1" value="' . $res['id'] . '"><b>' . $res['id'] . '</b></td>
        <td style="max-width: 100px;">' . ($tool && $tool['zid'] > 0 ? '<a href="./master.user.php?zid=' . $tool['zid'] . '" class="label label-success">' . $tool['zid'] . '</a>' : '<span class="label label-primary">主站商品</span>') . '</td>
        <td style="min-width: 260px;" class="text-break"><span onclick="showOrder(' . $res['id'] . ')" title="点击查看详情" class="btn btn-success btn-xs">详情</span>&nbsp;&nbsp;<span onclick="reShop(' . $res['id'] . ')" title="点击同步信息" class="btn btn-primary btn-xs">同步</span>&nbsp;<a href="javascript:show(' . $res['tid'] . ')" style="color:#000000">' . htmlspecialchars($cmy_func[$res['tid']], ENT_QUOTES, 'UTF-8') . '</a></td>
        <td style="min-width: 240px;" class="text-break">' . getOrderInput($res) . '</td>
        <td>' . getOrderPayType($res) . '</td><td><a class="btn btn-xs" style="background-color:' . getMoneyColor($res['money']) . ';color:#fff">' . $res['money'] . '</a></td>
        <td><a class="btn btn-xs" onclick="inputOrder(' . $res['id'] . ')" data-tip="点击可修改下单数据、份数等" title="点击修改数据" style="background-color:' . getNumColor($res['value']) . ';color:#fff">' . $res['value'] . '</a></td><td><a href ="sitelist.php?zid=' . $res['zid'] . '" target="_blank">' . $res['zid'] . '</a></span></td><td>' . $res['addtime'] . ($res['endtime'] ? '<br>' . $res['endtime'] : null) . '</td><td>' . display_djzt($res['djzt'], $res['id'], $res['status'], $res['djorder']) . '</td><td><a href="JavaScript:void(0)" onclick="javascript:setInfo(\'' . $res['id'] . '\',\'' . $res['status'] . '\')" title="点此填写处理信息">' . display_zt($res['status'], $res['id']) . '</a></td><td><select onChange="javascript:setStatus(\'' . $res['id'] . '\',this.value)" class="form-control"><option selected>更改订单状态</option><option value="0">改为待处理</option><option value="2">改为正在处理</option><option value="1">改为已完成</option><option value="3">改为异常</option><option value="4">改为已退款</option><option value="10">改为待退款</option>';
        if ($res['zid'] > 0) {
            echo '<option value="9">订单退款</option>';
        }
        echo '<option value="5">删除订单</option></select></td></tr>';

    }
}

echo <<<'html1'
            </tbody>
        </table>
        <div style="height: 100px;display: block;"></div>
html1;
if ($is_mb == false) {
    echo '<footer class="navbar-fixed-bottom">
            <div class="paging-navbar">';
} else {
    echo '<footer class="navbar-fixed-bottom">
            <div class="paging-navbar">';
}
echo <<<'html2'
<div class="form-inline">
            <input type="hidden" name="result_all" id="result_all"/>&nbsp;
            <input name="chkAll1" type="checkbox" id="chkAll1" onclick="check1(this.form.list1)" value="checkbox">&nbsp;反选&nbsp;
            <select name="checkStatus" class="form-control"><option selected>操作订单</option><option value="0">待处理</option><option value="2">正在处理</option><option value="1">已完成</option><option value="3">异常</option><option value="5">重新下单</option><option value="7">设置处理信息</option><option value="10">待退款</option><option value="4">已退款</option><option value="-1">删除订单</option></select>&nbsp;
            <button type="button" class="btn btn-primary btn-sm" onclick="operation()">确定</button>
            <div class="form-group" style="margin-left:10px">
html2;
$pageList = new \core\Page($numrows, $pagesize, 1, $link);
echo $pageList->showPage();
echo <<<'html3'
            </div>
            </div>
            </div>
        </footer>
      </div>
     </form>
     <script type="text/javascript" src="' . $cdnserver . 'assets/public/stable/Sortable.js?' . $jsver . '"></script>
     <script type="text/javascript">
     runTip();
     </script>
html3;
