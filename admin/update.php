<?php
!defined('DS') && define('DS', DIRECTORY_SEPARATOR);
if (ini_get('display_errors')) {
    ini_set('display_errors', 'off');
}
include '../includes/common.php';

$title = '检测更新';
checkLogin();

function zipExtract($src, $dest)
{
    $zip = new ZipArchive();
    if ($zip->open($src) === true) {
        $zip->extractTo($dest);
        $zip->close();
        return true;
    }
    return false;
}

function checkDir($dir = '')
{
    if (empty($dir)) {
        return false;
    }
    $dir     = str_replace('/', DS, $dir);
    $dir_new = '/';
    $arr     = explode(DS, $dir);
    foreach ($arr as $key => $value) {
        if ($value == "") {
            continue;
        }
        $dir_new .= $value . DS;
        if (!is_dir($dir_new)) {
            @mkdir($dir_new, 0755);
        }
    }
    return true;
}

function copyWebFile($filePath, $targetPath = '')
{
    if (function_exists("set_time_limit")) {
        @set_time_limit(600);
    }

    if (function_exists("ignore_user_abort")) {
        @ignore_user_abort(true);
    }
    $time = time();
    if (empty($targetPath)) {
        $local_path = dirname(__FILE__);
        $local      = $local_path . '/updatefile_' . substr(md5(rand(1, 9999999) . $time . ' 11111111'), 0, 14) . '.zip';
    } else {
        $local = $targetPath;
    }

    if (!file_exists($local_path)) {
        mkdir($local_path, 0755);
        @chmod($local_path, 0755);
    }
    ob_end_clean();
    ob_implicit_flush(1);
    ob_start();
   
    $header = get_headers($filePath, 1);
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $local . '"');
    header('Content-Length: ' . $header['Content-Length']);
    flush();
    readfile($filePath);
    $filedata = ob_get_contents();
    ob_clean();
    header('content-Type:text/html;charset=utf-8');

    if (file_put_contents($local, $filedata)) {
        @chmod($local_path, 0755);
        return true;
    }
    return '本地文件权限异常：' . $local;
}

function delUpdateFile()
{
   
    $FileTypeList = ['zip', '7z', 'rar', 'gz', 'tar'];
    $fileList     = scandir(dirname(__FILE__));
    foreach ($fileList as $filename) {
        $type = substr($filename, strripos($filename, ".") + 1);
        if (in_array($type, $FileTypeList)) {
            unlink($filename);
        }
    }
    unlink(ROOT . 'includes/core/softList.tester.txt');
    unlink(ROOT . 'includes/core/softList.updater.txt');
}

function setLog($msg, $type = 'Update')
{
    try {
        $log = new \core\Log(1, 10, $type);
        $log->setWriteParams(false);
        $log->add('更新日志', $msg);
    } catch (\Exception $e) {
        //
    }
}

function moveDir($dir, $newDir)
{
    if (!is_dir($dir)) {
        return false;
    }

    if (!is_dir($newDir)) {
        @mkdir($newDir);
    }

    $dh = opendir($dir);
    @chmod($dir, 0755);
    @chmod($newDir, 0755);
    while ($file = readdir($dh)) {
        if ($file != "." && $file != "..") {
            if (is_dir($dir . "/" . $file)) {
                moveDir($dir . "/" . $file, $newDir . "/" . $file);
            } else {
                $fullpath = $newDir . "/" . $file;
                $oldpath  = $dir . "/" . $file;
                @chmod($fullpath, 0755);
                @chmod($oldpath, 0755);
                copy($oldpath, $fullpath);
                unlink($oldpath);
            }
        }
    }

    closedir($dh);
    if (rmdir($dir)) {
        return true;
    } else {
        return false;
    }
}

function flushEcho($msg = '')
{
    //header('Content-Type: text/event-stream');
    header('Cache-Control: no-cache');
    header('X-Accel-Buffering: no');
    ob_end_clean();
    ob_implicit_flush(true);
    echo $msg;
}

$scriptpath = str_replace('\\', '/', $_SERVER['SCRIPT_NAME']);
$scriptpath = substr($scriptpath, 0, strrpos($scriptpath, '/'));
$admin_path = substr($scriptpath, strrpos($scriptpath, '/') + 1);

function checkZipExt()
{
    if (stripos(PHP_VERSION, '7.3') !== false) {
        return '注意：当前服务器环境是PHP7.3版本，在线更新可能会失效<br/>请先切换到5.4~7.2再操作，或手动下载更新';
    } elseif (stripos(PHP_VERSION, '7.4') !== false) {
        return '注意：当前服务器环境是PHP7.4版本，在线更新可能会失效<br/>请先切换到5.4~7.2再操作，或手动下载更新';
    }
}

$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;
if (isset($_GET['auto']) && $_GET['auto'] == 1) {
    $act = 'auto';
}
if ($act == "update") {
    $all_update = input('all_update');
    $updateData = checkUpdate(false, 1);
    if ($updateData['code'] == 3) {
        exit('{"code":-1,"msg":"' . $updateData['msg'] . '"}');
    } elseif ($all_update == 0 && $updateData['code'] != 1) {
        exit('{"code":3,"msg":"当前已是最新版本！"}');
    } else {
        exit('{"code":0,"msg":"succ","url":"./' . $admin_path . '/update.php"}');
    }
} elseif ($act == "auto") {
    $type = 'updater';
    $file = ROOT . 'includes/core/softList.' . $type . '.txt';
    $arr  = null;
    $time = time();
    if (is_file($file) && $content = file_get_contents($file)) {
        if (strlen($content) > 128) {
            $arr = @json_decode(base64_decode($content), true);
        }
    }
    header('Cache-Control: no-cache');
    header('X-Accel-Buffering: no');
    ob_end_clean();
    ob_implicit_flush(true);
    // include './head.php';
    // flushEcho('<div class="col-sm-12 col-md-12 col-lg-12 center-block" style="float: none;padding-top: 75px">');
    flushEcho('【更新提示】注意！程序正在自动更新中...<br/>');
    $server_url = getAuthServer(false, 'v1');
    if (!is_array($arr) || $time - strtotime($arr['updatetime']) > 60) {
        $url  = $server_url . 'Api/update.php?act=getFileList&authcode=' . $authcode . '&url=' . $_SERVER['HTTP_HOST'];
        $opts = array(
            'http' => array(
                'method'  => "GET",
                'timeout' => 30, //单位秒
            ),
        );
        $text = file_get_contents($url, false, stream_context_create($opts));

        $arr = json_decode($text, true);
        if (!is_array($arr)) {
            $text = get_curl($url);
            $arr  = json_decode($text, true);
            if (!is_array($arr)) {
                $arr = ['code' => -1, 'msg' => '打开网站超时或当前人数过多，请稍后再试'];
            } else {
                @file_put_contents(ROOT . 'includes/core/softList.' . $arr['type'] . '.txt', base64_encode($text));
            }
        } else {
            @file_put_contents(ROOT . 'includes/core/softList.' . $arr['type'] . '.txt', base64_encode($text));
        }
    }
    sleep(1);

    flushEcho('【更新提示】提示更新成功前请不要关闭本页面！<br/>');
    sleep(1);
    flushEcho('【更新提示】自动更新只会更新需要更新的文件！<br/>');
    sleep(1);
    $title = '自动更新中';
    if (is_array($arr)) {
        if ($arr['code'] == 0) {
            $server_url2 = $server_url;
            $count       = count($arr['data']);
            flushEcho('【更新提示】远程文件列表获取成功，共' . $count . '个文件！<br/>');
            flushEcho('【更新提示】正在执行更新列表中的文件！<br/>');
            $num   = isset($_GET['num']) && $_GET['limit'] > 0 ? intval($_GET['num']) : 0;
            $limit = isset($_GET['limit']) && $_GET['limit'] > 10 ? intval($_GET['limit']) : 300;
            $max   = isset($_GET['max']) ? $_GET['max'] : 30;
            $ok    = 1;
            $size  = 0;
            if ($num >= $count) {
                sleep(1);
                flushEcho('【更新提示】自动更新完成，感谢使用斑马云商城Plus！<a href="./list.php">回到订单管理</a><br/>');
                die();
            }
            for ($i = 0; $i < $limit; $i++) {
                if ($ok >= $max) {
                    break;
                }
                if ($num >= $count) {
                    $num++;
                    break;
                }
                $num++;
                if (isset($arr['data'][$num + $i])) {
                    $item     = $arr['data'][$num + $i - 1];
                    $filename = ltrim($item['path'], '/');
                    $filepath = ROOT . $filename;
                    if (stripos($filepath, 'admuser') !== false) {
                        $filepath = str_replace('admuser', $admin_path, $filepath);
                    }
                    $filesize = filesize($filepath);
                    if (!is_file($filepath) || abs($filesize - $item['info']['size']) >= 2) {
                        //判断文件需要是否更新
                        $durl = $server_url2 . 'Api/update.php?act=getFileContent';
                        $durl .= '&path=' . base64_encode($filename) . '&authcode=' . $authcode . '&url=' . $_SERVER['HTTP_HOST'];
                        //$ret = true;
                        \core\File::checkDir(dirname($filepath));
                        $ret = \core\File::copyWebFileByGet($durl, $filepath);
                        // if (class_exists('\core\File')) {

                        // } else {
                        //     checkDir(dirname($filepath));
                        //     $ret = copyWebFile($durl, $filepath);
                        // }
                        if ($ret !== true && !is_string($ret)) {
                            $ret = '本地权限异常:' . $filepath;
                        }

                        $size = $size + floatval($item['size']);
                        if ($ret === true) {
                            flushEcho('【更新日志】未更新文件 => ' . $filename . ' 更新成功！<br/>');
                        } else {
                            flushEcho('【更新日志】未更新文件 => ' . $filename . ' 更新失败，' . $ret . '<br/>');
                        }
                        if ($item['size'] > 150 || $size > 400) {
                            break;
                        }
                    }
                }
            }
            $url = './update.php?auto=1&num=' . $num . '&limit=' . $limit . '&max=' . $max;
            flushEcho('【更新日志】本轮更新完毕，即将跳转到下一轮！<br/>');
            sleep(3);
            echo '<script>window.location.href=\'' . $url . '\';</script>';
        } else {
            showmsg('自动更新失败，' . $arr['msg'], 3);
        }
    } else {
        showmsg('自动更新失败，请稍后再试或手动更新！', 3);
    }
} elseif ($act == "updateNew") {
    $data  = input('post.data', 1);
    $start = intval($data['start']);
    $limit = intval($data['limit']);
    if ($limit < 300) {
        $limit = 300;
    }
    $type    = isset($data['type']) ? $data['type'] : 'updater';
    $file    = ROOT . 'includes/core/softList.' . $type . '.txt';
    $content = '';
    $arr     = null;
    $size    = 0;
    $timeout = 0;
    $time    = time();
    if (is_file($file) && $content = file_get_contents($file)) {
        if (strlen($content) > 128) {
            $arr = @json_decode(base64_decode($content), true);
        }
    }
    $server_url = getAuthServer(false, 'v1');
    $url        = $server_url . 'Api/update.php?act=getFileList&authcode=' . $authcode . '&url=' . $_SERVER['HTTP_HOST'];
    if (!is_array($arr) || $time - strtotime($arr['updatetime']) > 60) {
        $text = cm_curl($url, 30);
        $arr  = json_decode($text, true);
        if (!is_array($arr)) {
            $arr = ['code' => -1, 'msg' => '当前服务器不支持在线更新，请手动下载更新包覆盖更新', 'result' => mb_substr($text, 0, 300)];
        } else {
            @file_put_contents(ROOT . 'includes/core/softList.' . $arr['type'] . '.txt', base64_encode($text));
        }
    }

    $result         = [];
    $result['msg']  = 'succ';
    $result['code'] = -1;
    $result['data'] = [];
    if (is_array($arr)) {
        if ($arr['code'] == 0) {
            $server_url2 = getAuthServer(false, 'v2');
            $count       = count($arr['data']);
            $last        = [];
            $list_succ   = [];
            $list_warn   = [];
            // $next        = [];
            // if ($start + $limit + 1 <= $count) {
            //     for ($i = 0; $i < $limit; $i++) {
            //         if (isset($arr['data'][$start + $limit + $i])) {
            //             $item     = $arr['data'][$start + $limit + $i];
            //             $filename = $item['path'];
            //             $filepath = ROOT . ltrim($filename, '/');
            //             $durl     = $server_url2 . 'Api/update.php?act=getFileContent';
            //             $durl .= '&path=' . base64_encode($filename);
            //             $next[$i] = [
            //                 'durl'     => $durl,
            //                 'filename' => $filename,
            //                 'filepath' => $filepath,
            //             ];
            //         }
            //     }
            // }
            $now        = $start;
            $white_list = [
                'includes/version.php',
                'includes/common.php',
                'ajax.php',
            ];
            $durl = $server_url2 . 'Api/update.php?act=getFileContent';
            for ($i = 0; $i < $limit; $i++) {
                $now += 1;
                if ($now <= $count && isset($arr['data'][$now - 1])) {
                    $item     = $arr['data'][$now - 1];
                    $filename = ltrim($item['path'], '/');
                    $filepath = ROOT . $filename;
                    if (stripos($filepath, 'admuser') !== false) {
                        $filepath = str_replace('admuser', $admin_path, $filepath);
                    }
                    $filesize   = filesize($filepath);
                    $needUpdate = false;
                    if (!is_file($filepath) || ($filesize - $item['info']['size']) != 0) {
                        $needUpdate = true;
                    }

                    if ($needUpdate) {
                        setLog(
                            "开始执行\n|-更新文件名称：" . $filename
                            . "\n|-索引：" . $now . "; 累计：" . $count
                            . "\n|-本地文件路径：" . $filepath
                            . "\n|-本地文件大小：" . $filesize
                            . "\n|-更新文件索引：" . ($now - 1)
                            . "\n|-是否需要更新：是"
                            . "\n|-下一个文件信息：" . ($now < $count ? $arr['data'][$now]['path'] : '无'),
                            'UpdateStart'
                        );
                    } else {
                        setLog(
                            "开始执行\n|-更新文件名称：" . $filename
                            . "\n|-本地文件路径：" . $filepath
                            . "\n|-索引：" . $now . "; 累计：" . $count
                            . "\n|-本地文件大小：" . $filesize . ';云端文件大小：' . $item['info']['size']
                            . "\n|-下一个文件信息：" . ($now < $count ? $arr['data'][$now]['path'] : '无'),
                            'UpdateStart'
                        );
                    }

                    if ($needUpdate || in_array($filename, $white_list)) {
                        setLog("更新执行日志\n|开始更新", 'UpdateRun');
                        //判断文件需要是否更新
                        $durl = $durl . '&path=' . base64_encode($filename) . '&authcode=' . $authcode . '&url=' . $_SERVER['HTTP_HOST'];
                        $ret  = true;
                        setLog("更新执行日志\n|更新日志：" . $durl, 'UpdateRun');
                        try {
                            \core\File::checkDir(dirname($filepath));
                            $ret = \core\File::copyWebFileByGet($durl, $filepath, $item['info']);
                        } catch (\Exception $e) {
                            $ret = '运行错误:' . $e->getMessage();
                        }

                        if ($ret !== true && !is_string($ret)) {
                            $ret = '文件权限异常，请尝试手动创建文件夹路径:' . $filepath;
                        }

                        setLog("更新执行结束\n|-本地文件路径：" . $filepath . "\n|-更新结果：" . (is_string($ret) ? $ret : '更新成功'), 'UpdateEnd');
                        $row = [
                            //'durl'        => $durl,
                            'name'        => $filename,
                            'size_update' => $item['info']['size'],
                            'size_local'  => $filesize,
                            'size_value'  => abs($filesize - $item['info']['size']) . 'bit',
                        ];
                        $row['status'] = $ret === true ? '更新成功' : $ret;
                        //die($filepath . '|' . date("Y-m-d H:i:s", filemtime($filepath)));
                        $size = $size + floatval($item['size']);
                        array_push($list_succ, $row);
                        if ($item['size'] > 150 || $size > 400) {
                            break;
                        }
                    } else {
                        if (count($list_warn) < 10) {
                            $list_warn[] = [
                                //'durl'        => $durl,
                                'name'        => $filename,
                                'size_update' => $item['info']['size'],
                                'size_local'  => $filesize,
                                'size_value'  => abs($filesize - $item['info']['size']) . 'bit',
                            ];
                        }
                    }
                }
            }

            if ($now >= $count) {
                $result['msg'] = '程序已成功更新到' . $arr['ver'] . '！感谢使用斑马云商城Plus';
            }

            $size           = round($size / 1024, 2);
            $result['code'] = 0;
            $result['data'] = [
                'type'       => $arr['type'],
                'start'      => $now,
                'now'        => $now,
                'count'      => $count,
                'size_count' => $arr['size'],
                'size_now'   => round($size + floatval($data['size_now']), 2),
                //'last'      => $last,
                'list_succ'  => $list_succ,
                'list_warn'  => $list_warn,
                //'next'      => $next,
            ];

            $result['data']['size_need'] = round(floatval($arr['size']) - $result['data']['size_now'], 2) . 'MB';
            $result['data']['size_now'] .= 'MB';
            $timeout                   = time() - $time;
            $result['data']['usetime'] = $timeout . '秒';
            $result['time']            = $date;
            $result['server']          = $server_url2;
            if ($timeout >= 50) {
                $result['data']['needtime'] = '40分钟左右';
            } elseif ($timeout >= 30) {
                $result['data']['needtime'] = '30分钟左右';
            } elseif ($timeout >= 25) {
                $result['data']['needtime'] = '20分钟内';
            } elseif ($timeout >= 20) {
                $result['data']['needtime'] = '15分钟内';
            } elseif ($timeout >= 10) {
                $result['data']['needtime'] = '10分钟内';
            } elseif ($timeout >= 5) {
                $result['data']['needtime'] = '5分钟内';
            } else {
                $result['data']['needtime'] = '2分钟内';
            }
            // $result['data']['needtime'] = intval($result['data']['size_need'] / $size * $timeout * 1.3 / 60);
            saveSetting('index_jsver', date('YmdHis'));
            exit(json_encode($result));
        } else {
            $arr['api_url']   = $url;
            $result['result'] = $arr;
            $result['msg']    = isset($arr['msg']) ? $arr['msg'] : '更新服务器维护中，请稍后再试！';
            exit(json_encode($result));
        }
    } else {
        $result['code'] = -2;
        $result['data'] = $data;
        $result['msg']  = $arr['msg'];
        exit(json_encode($result));
    }
}
include './head.php';

delUpdateFile();
$data     = checkUpdate('update');
$checkMsg = '';
//$checkMsg = checkZipExt();

echo ' <div class="col-sm-12 col-md-12 col-lg-12 center-block" style="float: none;padding-top: 10px">

      <div class="block">
        <div class="block-title"><h3 class="panel-title">在线更新</h3>
</div>
<div class="">
    <div class="alert alert-info" id="update-msg">
        ' . ($checkMsg != '' ? '<li class="list-group-item" style="color:red">' . $checkMsg . '</li>' : null) . '
        ' . $data['msg'] . '<br>
        程序名称：斑马云商城Plus<br>
        程序作者：斑马科技<br>
        程序声明：禁止将本程序用于任何违法活动或宣传违法内容，违者将取消一切权利，并保留相关证据<br>
        安装说明：上传到空间后直接访问即可根据提示安装<br>
        服务器环境要求：PHP7.0~php8.0<br>
        数据库环境要求：支持Pdo-Mysql（主流主机和宝塔面板基本自带，提示没有请联系主机客服）<br>
    </div>
    <form action="" method="POST" role="form">
        <p><a class="btn btn-primary form-control updateSubmit">立即更新</a></p>';
echo '
    </form>
    <div id="update-info" class="alert" style="background-color:#F8F8FF;color: #0c69c6;">
        ' . $data['uplog'] . '
    </div>
</div>
</div>
</div>
</div>
<script src="./assets/js/update.js?' . $jsver . '"></script>
<script type="text/javascript">
if (typeof pageLoad == "undefined" || pageLoad !== true) {
    layer.alert("缺少静态js文件，请前往【修复工具】修复或【主站设置->系统在线更新】操作后再试！");
}
</script>';
echo ' <div class="col-sm-12 col-md-12 col-lg-12 center-block">
     <div class="block">
        <div class="">
          <div class="alert alert-info">
V1.3.0（20250228）<br>
1.【修复】修复几处已知bug<br>
2.【修复】供货商添加商品无法显示二级分类<br>
3.【新增】前后端网盘投诉功能<br>
4.【新增】前后端引流举报功能<br>
5.【修复】未正常完成订单无法使用快捷模版回复<br>
6.【优化】数据库字段<br>
7.【新增】分站bug投诉与建议功能<br>
8.【新增】提现代付接口<br><br>

V1.2.9（20250220）<br>
1.【修复】商品排序错乱<br>
2.【修复】手动退款不自动扣除上级提成<br>
3.【新增】商品分类和商品名称可选颜色<br>
4.【新增】分类列表可设置为禁用<br>
5.【新增】商品点赞与投诉排行，供货商订单与销售排行<br>
6.【新增】App生成接口<br>
7.【新增】商品可设置为禁用<br>
8.【优化】后台主页数据显示<br>
9.【新增】供货商商品列表管理可再次切换审核状态<br>
10.【新增】供货商商品列表管理可通过审核状态筛选查看<br>
11.【新增】供货商商品审核时根据审核结果自动上下架<br><br>

V1.2.8（20250220）<br>
1.【修复】部分供货商的订单提成没有<br>
2.【修复】代理提现操作驳回后代理分站余额不到账<br>
3.【新增】押金排序优先级设置，可选择按商品排序或按押金为最大优先级<br>
4.【调整】上架日志显示商品分类<br>
5.【优化】其他几处小优化<br>
6.【新增】供货商单笔最低提现金额设置<br><br>

V1.2.7（20250213）<br>
1.【新增】新上架商品自动排序开关设置，可选择关闭、自动置顶和置底<br>
2.【调整】库存预警只检测已经审核过的非对接商品<br>
3.【调整】对接商品不会出现在供货审核列表<br>
4.【优化】新上架商品自动排序性能<br>
5.【修复】供货商后台新上架的商品不会立即提示库存预警<br>
6.【新增】供货商商品删除权限开关设置<br>
7.【修复】一处因第三方系统转移导致的卡密发货bug<br><br>

V1.2.6（20241214）<br>
1.【修复】供货商后台新上架的商品不会立即提示库存预警<br>
2.【修复】部分供货商的订单提成没有<br>
3.【修复】代理提现操作驳回后代理分站余额不到账<br>
4.【调整】库存预警只检测已经审核过的非对接商品<br>
5.【调整】对接商品不会出现在供货审核列表<br><br>

V1.2.5（20241212）<br>
1.【新增】新版批量上架，可上架到指定本地分类<br>
2.【修复】新版批量上架bug<br>
3.【优化】库存预警检测逻辑和提示<br>
4.【优化】其他几处修复优化<br><br>

V1.2.4（20240913）<br>
1.【修复】管理后台添加商品失败<br><br>

V1.2.3（20240912）<br>
1.【修复】付款时提示商户信息异常<br><br>

V1.2.2（20240826）<br>
1.【优化】一处问题<br><br>

V1.2.1（20240809）<br>
1.【修复】批量对接克隆和自主上架的商品前台商品列表看不到<br>
2.【新增】后台商品列表改为主站商品批量操作<br><br>

V1.2.0（20240814）<br>
1.【修复】几个已知bug<br><br>

V1.1.9（20240809）<br>
1.【修复】供货商/分站提现按提现方式筛选结果不对，批量操作完成后不显示完成时间<br>
2.【修复】供货商/分站提现管理电脑端支持快速查看全部状态数据<br>
3.【修复】商品动态日志的自动日志没效果<br>
4.【修复】分站列表已设置密价的查询不到<br><br>

V1.1.8（20240809）<br>
1.【修复】供货商/分站提现处理中的数据查看不了<br>
2.【新增】供货商/分站提现管理电脑端支持快速查看全部状态数据<br>
3.【优化】后台多处表格列表手机端的批量操作<br>
4.【修复】前台模板的音乐按钮暂停无效<br>
5.【修复】子目录下搭建的分站部分跳转链接错误<br><br>

V1.1.7（20240808）<br>
1.【修复】供货商/分战提现的提现方式是微信时也显示支付宝<br>
2.【修复】对接订单状态自动同步<br>
3.【修复】对接添加同系统/彩虹系统卡密商品时，自动选中的商品类型不对<br>
4.【优化】后台订单处理信息填写输入框比例大小<br>
5.【修复】供货商商品不能删除<br>
6.【修复】部分页面跳转链接地址不对<br>
7.【新增】分站列表批量操作, 支持修改分站域名、重置密价、修改分站名称等<br><br>

V1.1.6（20240806）<br>
1.【修复】手机端商品详情弹窗不适配<br>
2.【修复】多处弹窗样式问题<br>
3.【修复】商品置顶排序反的（注意, 商品排序新增了设置正序和倒序两种可自由切换）<br>
4.【修复】分站推广链接多了http，导致打不开<br>
5.【修复】订单状态同步，状态码写错，导致同步不了<br>
6.【修复】商品动态删除不了<br>
7.【修复】短信邮件记录删除不了<br>
8.【更新】短信邮件记录批量删除和快速清理<br>
9.【修复】首页加密模块开启后出现两个页面<br>
10.【修复】分站域名自助修改始终只能修改第二个<br>
11.【修复】分站音乐链接填写无效<br>
12.【修复】分站后台消息通知无法一键已读<br>
13.【修复】其他已知若干bug<br><br>

V1.1.5（20240805）<br>
1.【更新】分站后台首页部分功能, 自助新增域名和更换域名功能, 可自定义更换二个域名之一<br>
2.【修复】在二级目录搭建下部分跳转链接错误<br>
3.【修复】在二级目录搭建下分站显示域名会打不开<br><br>

V1.1.4（20240804）<br>
1.【新增】商品动态可手动发布, 某天未发布时则自动获取监控变动日志<br>
2.【新增】内置首页源代码加密模块功能, 可自定义是否开启<br>
3.【修复】供货商提现功能几处bug<br><br>

V1.1.3（20240803）<br>
1.【修复】一个已知bug<br><br>

V1.1.2（20240803）<br>
1.【修复】易支付多接口回调验证失败<br>
2.【修复】供货商保证金编辑后最大不能超过127<br>
3.【修复】自动更新数据库没更新成功<br>
4.【修复】供货商充值余额到账、以及明细不对<br>
5.【更新】分站提成转余额<br><br>

V1.1.2（20240803）<br>
1.【修复】供货商充值页面没有支付方式按钮<br>
2.【修复】供货商提现页面驳回时不会显示原因<br>
3.【更新】供货商上架/编辑商品可通知管理员, 主站可设置是否通知<br>
4.【更新】主站后台卡密库存管理可筛选是否主站商品和供货商商品<br>
5.【优化】供货商手机号登录<br><br>

V1.1.1（20240803）<br>
1.【更新】分站/用户提现管理，增加批量操作、多种筛选方式、增加下载支付宝批量转账模板<br>
2.【更新】供货商提现管理，增加批量操作、多种筛选方式、增加下载支付宝批量转账模板<br>
3.【更新】覆盖更新包可自动更新数据库<br>
4.【修复】供货商后台弹窗公告没生效<br>
5.【更新】分站后台可设置自定义客服或链接、音乐链接<br><br>

V1.1.0（20240803）<br>
1.【更新】易支付自动多接口, 支持前台、分站后台、供货商充值、满多少钱等多个场景可使用独立接口，支持随机轮训<br>
2.【更新】供货商商品审核时可修改加价模板和成本价格<br><br>

V1.0.9（20240802）<br>
1.【优化】后台管理员资料修改邮箱验证逻辑<br>
2.【更新】后台商品管理多种查询筛选方式<br>
3.【修复】供货商系统几处bug<br>
4.【修复】后台卡密库存添加后返回上一页卡密还在<br>
5.【更新】登录通知邮件内容包含登录IP地址、IP城市信息<br>
6.【更新】工单待处理、分站提现、供货商提现、待处理订单醒目提醒<br><br>

V1.0.8（20240801）<br>
1.【新增】供货待审核商品左侧菜单实时小红点提示, 并显示待审核数量<br><br>

V1.0.7（20240801）<br>
1.【新增】全站商品库存预警通知<br>
2.【优化】供货商商品库存预警邮件通知<br>
3.【修复】分站设置音乐链接会报错<br>
4.【修复】数据迁移工具在转移某个空表没有数据的情况下会一直转移<br><br>

V1.0.6（202407231）<br>
1.【优化】数据迁移功能兼容性<br>
2.【修复】主站后台商品列表和供货商商品列表没显示库存<br><br>

V1.0.5（202407230）<br>
1.【修复】几个已知bug<br><br>

V1.0.4（202407230）<br>
1.【修复】几个已知bug<br>
2.【新增】商品可自定义开启点赞功能<br><br>

V1.0.3（20240727）<br>
1.【修复】几个已知bug<br>
2.【更新】主站管理员邮件提醒【包含登录操作、改密操作、修改提现资料等】<br>
3.【更新】供货商邮件提醒【包含登录操作、提现操作、改密操作、修改提现资料等】<br><br>

V1.0.2（20240726）<br>
1.【更新】供货商更新库存不足通知, 支持邮件通知<br>
2.【更新】供货商安全验证，支持提现操作、改密操作、修改提现资料<br><br>

V1.0.0（20240725）<br>
1.【新增】供货商系统, 支持卡密管理、库存不足提醒、营业统计<br>
2.【更新】短信模块、邮件模块，都支持模板编辑、短信模板支持一键提交同步审核【短信宝不支持】<br>
3.【重构】数据库快捷类，优化数据库事务自动管理，提高性能<br>
4.【重构】下单接口，多个支付回调接口，提高性能<br><br></div>
    </div></div>
  </div>
</div>';