function show(tid) {
    var ii = layer.load(2, {
        shade: [0.1, '#fff']
    });
    var area = [$(window).width() > 640 ? '480px' : '95%', 'auto'];
    $.ajax({
        type: 'GET',
        url: 'ajax.php?act=getTool&tid=' + tid,
        dataType: 'json',
        success: function (data) {
            layer.close(ii);
            if (data.code == 0) {
                var item = '<table class="table table-condensed table-hover">';
                item += '<tr><td colspan="6" style="text-align:center"><b>商品详情</b></td></tr><tr><td class="info">商品ID</td><td colspan="5">' + data.data.tid + '</td></tr><tr><td class="info">商品名称</td><td colspan="5">' + data.data.name + '</td></tr><tr><td class="info">商品链接</td><td colspan="5"><a href="' + data.data.link + '" target="_blank">' + data.data.link + '</a></td></tr>';
                 if (data.data.desc && data.data.desc !== "null") {
                    var shopEditUrl = './shopedit.php?my=edit&tid=' + data.data.tid;
                    item += '<tr><td class="info">商品介绍</td><td colspan="5"><a href="' + shopEditUrl + '" style="color: inherit; text-decoration: none;" target="_blank">' + decodeURI(data.data.desc) + '</a></td></tr>';
               }
                item += '</table>';
                layer.open({
                    type: 1,
                    title: '商品详情',
                    area: area,
                    skin: 'layui-layer-rim',
                    offset: '5px',
                    content: '<div style="max-height: 95vh; overflow-y: auto; padding: 5px;">' + item + '</div>', 
                    success: function(layero, index){
                        $('body').css('overflow', 'auto');
                    }
                });
            } else {
                layer.alert(data.msg);
            }
        },
        error: function (data) {
            layer.msg('服务器错误');
            return false;
        }
    });
}