<?php
echo '
 <div style="height:120px;display: block;">
<footer class="text-center" style="margin:10px auto">
	&copy; 2023~' . date('Y') . ' Powered by <a onclick="return false;">斑马云商城</a>!
</footer>
</div>
';

if ($editor_load == true && $conf['editor_index'] == 1) {
    echo '
	  <script src="' . $cdnserver . 'assets/public/wangEditor/3.1.1/wangEditor.js?' . $jsver . '" type="text/javascript"></script>
	  <script src="' . $cdnserver . 'assets/public/wangEditor/3.1.1/plugin.js?' . $jsver . '" type="text/javascript"></script>
	  ';
}

echo '
<script type="text/javascript">
';
if ($editor_load == true && $conf['editor_index'] == 1) {
    echo '
	var $is_mb = \'' . ($is_mb === true ? '1' : '0') . '\';
	var E = window.wangEditor ? window.wangEditor : null;
	var editor2,editor3,editor4,editor5,content,content2,content3,content4;
	';
}

echo '
window.onload=function(){
	var items = $("select[default]");
	for (i = 0; i < items.length; i++) {
	    $(items[i]).val($(items[i]).attr("default")||0);
	    if(typeof(changeEvent)=="undefined" || changeEvent!==false){
	    	$(items[i]).change();
	    }
	}
';

if ($editor_load == true && $conf['editor_index'] == 1) {
    echo '
	//console.log($(\'.textDom\'));
	//console.log($(\'.textDom2\'));
	if ($(\'.textDom\').length>0 && "function" === typeof window.wangEditor) {
	    if ($(\'.textDom\').length > 0) {
			content = $(\'.textDom\');
		}
		$("#editorBox").show();
		editor2 = new E(\'#editorBox\');
		if(typeof boxType != "undefined"){
			$(\'#editorBox\').css({\'display\':\'block\',\'width\':\'100%\',\'float\':\'none\'});
		}
		editor2.customConfig.uploadImgServer = \'upload.php\';
		editor2.customConfig.uploadImgTimeout = 60 * 1000;
		editor2.customConfig.uploadFileName = \'filedata\';
		editor2.customConfig.mobile = $is_mb;
		editor2.customConfig.uploadImgHooks = {
		    success: function (xhr, editor, result) {
		    	   if (result.errno==0) {
		             layer.msg("图片插入成功");
		    	   }
		    	   else{
		    	   	    if (!result.msg) {
			             	result.msg="上传图片失败！请检查";
			    	    }
			    	    layer.alert(result.msg);
		    	   }
		    },
		    timeout: function (xhr, editor) {
		        layer.alert("图片上传超时，如文件过大请尝试剪切或压缩到较小后再试！");
		    },
		    fail: function (xhr, editor, result) {
				 if("object" == typeof result && "undefined" !=typeof result.msg){
				 	layer.alert(result.msg);
				 }
				 else{
				 	layer.alert("上传失败，请在浏览器按F12切换到network截图【上传请求的结果】在工单反馈");
				 }
		    }
		}
		editor2.customConfig.onchange = function (newHtml) {
		    if (typeof content =="object") {
		    	!content.val(""+newHtml) && layer.msg("编辑内容同步失败，请截图并提供详细描述提交工单反馈");
		    	return;
		    }
		    layer.msg("编辑内容同步失败，请截图并提供详细描述提交工单反馈");
		    return;
		}
		editor2.customConfig.onblur = function (newHtml) {
		    // 检测无用html内容
            if (editor2.txt.isEmptyHtml(newHtml)) {
                newHtml = "";
                editor2.txt.html("");
                editor2.txt.change();
            }
		    return;
		}

		if(typeof(editorMenu)=="object" && editorMenu instanceof Array){
			editor2.customConfig.menus = editorMenu;
		}
		editor2.create();
		if (content.val()!="") {
			editor2.txt.html(content.val());
		}
		else{
			editor2.txt.html("");
		}

		E.viewSource.init(editor2);

	}

	//商品页面附加富文本
	if ($(\'.textDom2\').length>0 && "function" === typeof window.wangEditor) {
		if ($(\'.textDom2\').length>1) {
			content2 = $(\'.textDom2\')[0];
		}
		else{
			content2 = $(\'.textDom2\');
		}

		editor3 = new E(\'#editorBox2\');
		if(typeof boxType != "undefined"){
			$(\'#editorBox2\').css({\'display\':\'block\',\'width\':\'100%\',\'float\':\'none\'});
		}
		editor3.customConfig.uploadImgServer = \'upload.php\';
		editor3.customConfig.uploadImgTimeout = 60 * 1000;
		editor3.customConfig.uploadFileName = \'filedata\';
		editor3.customConfig.mobile = $is_mb;
		editor3.customConfig.uploadImgHooks = {
		    success: function (xhr, editor, result) {
		    	   if (result.errno==0) {
		             layer.msg("图片插入成功");
		    	   }
		    	   else{
		    	   	   if (!result.msg) {
				            result.msg="上传图片失败！请检查";
				    	   }
				    	   layer.alert(result.msg);
		    	   }
		    },
		    timeout: function (xhr, editor) {
		        layer.alert("图片上传超时，如文件过大请尝试剪切或压缩到较小后再试！");
		    },
		    fail: function (xhr, editor, result) {
		        layer.alert("图片插入返回状态异常，请反馈作者处理！<br>返回信息：" + result);
		    }
		}
		if(typeof(editorMenu)=="object" && editorMenu instanceof Array){
			editor3.customConfig.menus = editorMenu;
		}
		editor3.customConfig.onchange = function (newHtml) {
		    if (typeof content2 =="object") {
		    	!content2.val(""+newHtml) && layer.msg("编辑内容同步失败，请截图并提供详细描述提交工单反馈");
		    	return;
		    }
		    layer.msg("编辑内容同步失败，请截图并提供详细描述提交工单反馈");
		    return;
		}
		editor3.customConfig.onblur = function (newHtml) {
		    // 检测无用html内容
            if (editor3.txt.isEmptyHtml(newHtml)) {
                newHtml = "";
                editor3.txt.html("");
                editor3.change();
            }
		    return;
		}
		editor3.create();
		if (content2.val()!="") {
			editor3.txt.html(content2.val());
		}
		else{
			editor3.txt.html("");
		}


		E.viewSource.init(editor3);

	}

    //商品页面附加富文本
	if ($(\'.textDom3\').length>0 && "function" === typeof window.wangEditor) {
		if ($(\'.textDom3\').length>1) {
			content3 = $(\'.textDom3\')[0];
		}
		else{
			content3 = $(\'.textDom3\');
		}

		editor4 = new E(\'#editorBox3\');
		if(typeof boxType != "undefined"){
			$(\'#editorBox3\').css({\'display\':\'block\',\'width\':\'100%\',\'float\':\'none\'});
		}
		editor4.customConfig.uploadImgServer = \'upload.php\';
		editor4.customConfig.uploadImgTimeout = 60 * 1000;
		editor4.customConfig.uploadFileName = \'filedata\';
		editor4.customConfig.mobile = $is_mb;
		editor4.customConfig.uploadImgHooks = {
		    success: function (xhr, editor, result) {
		    	   if (result.errno==0) {
		             layer.msg("图片插入成功");
		    	   }
		    	   else{
		    	   	   if (!result.msg) {
				            result.msg="上传图片失败！请检查";
				    	   }
				    	   layer.alert(result.msg);
		    	   }
		    },
		    timeout: function (xhr, editor) {
		        layer.alert("图片上传超时，如文件过大请尝试剪切或压缩到较小后再试！");
		    },
		    fail: function (xhr, editor, result) {
		        layer.alert("图片插入返回状态异常，请反馈作者处理！<br>返回信息：" + result);
		    }
		}
		if(typeof(editorMenu)=="object" && editorMenu instanceof Array){
			editor4.customConfig.menus = editorMenu;
		}
		editor4.customConfig.onchange = function (newHtml) {
		    if (typeof content3 =="object") {
		    	!content3.val(""+newHtml) && layer.msg("编辑内容同步失败，请截图并提供详细描述提交工单反馈");
		    	return;
		    }
		    layer.msg("编辑内容同步失败，请截图并提供详细描述提交工单反馈");
		    return;
		}
		editor4.customConfig.onblur = function (newHtml) {
		    // 检测无用html内容
            if (editor4.txt.isEmptyHtml(newHtml)) {
                newHtml = "";
                editor4.txt.html("");
                editor4.change();
            }
		    return;
		}
		editor4.create();
		if (content3.val()!="") {
			editor4.txt.html(content3.val());
		}
		else{
			editor4.txt.html("");
		}


		E.viewSource.init(editor4);

	}

	if( "function" !== typeof window.wangEditor ){
		if ($(\'.textDom\').length > 0) {
			if("function" == typeof($(\'.textDom\').removeClass)){
				$(\'.textDom\').removeClass("hide").addClass("form-control").attr("rows", "6").show();
			}
			else{
				$(\'.textDom\').attr("class", "form-control textDom").attr("rows", "6").show();
			}
		}

		if ($(\'.textDom2\').length > 0) {
			if("function" == typeof($(\'.textDom2\').removeClass)){
				$(\'.textDom2\').removeClass("hide").addClass("form-control").attr("rows", "6").show();
			}
			else{
				$(\'.textDom2\').attr("class", "form-control textDom2").attr("rows", "6").show();
			}
		}

        if ($(\'.textDom3\').length > 0) {
			if("function" == typeof($(\'.textDom3\').removeClass)){
				$(\'.textDom3\').removeClass("hide").addClass("form-control").attr("rows", "6").show();
			}
			else{
				$(\'.textDom3\').attr("class", "form-control textDom3").attr("rows", "6").show();
			}
		}
	}
';
} else {
    echo '
	if ($(\'.textDom\').length > 0) {
		if("function" == typeof($(\'.textDom\').removeClass)){
			$(\'.textDom\').removeClass("hide").addClass("form-control").attr("rows", "6").show();
		}
		else{
			$(\'.textDom\').attr("class", "form-control textDom").attr("rows", "6").show();
		}
	}

	if ($(\'.textDom2\').length > 0) {
		if("function" == typeof($(\'.textDom2\').removeClass)){
			$(\'.textDom2\').removeClass("hide").addClass("form-control").attr("rows", "6").show();
		}
		else{
			$(\'.textDom2\').attr("class", "form-control textDom2").attr("rows", "6").show();
		}
	}

    if ($(\'.textDom3\').length > 0) {
		if("function" == typeof($(\'.textDom3\').removeClass)){
			$(\'.textDom3\').removeClass("hide").addClass("form-control").attr("rows", "6").show();
		}
		else{
			$(\'.textDom3\').attr("class", "form-control textDom3").attr("rows", "6").show();
		}
	}
';
}
echo '
}
function editorChange(){
	if(typeof(editor2) =="object" && typeof(editor2.txt.html) =="function"){
		//将商品简介同步到表单
		var text = $(content).val();
		if(text != ""){
        	editor2.txt.html(text, false);
        }
	}

	if(typeof editor3 =="object" && typeof editor3.txt.html =="function"){
		//将提示内容同步到表单
		var text = $(content2).val();
        if(text != ""){
        	editor3.txt.html(text, false);
        }
	}

    if(typeof editor4 =="object" && typeof editor4.txt.html =="function"){
		//将提示内容同步到表单
		var text = $(content3).val();
        if(text != ""){
        	editor4.txt.html(text, false);
        }
	}
}
$(document).on("click", ".emptyText", function(event) {
    event.preventDefault();
    /* Act on the event */
    $(content).val("");
    editor2.txt.html("", false);
});
$(document).on("click", ".emptyText2", function(event) {
    event.preventDefault();
    /* Act on the event */
    $(content2).val("");
    editor3.txt.html("", false);
});

$(document).on("click", ".emptyText3", function(event) {
    event.preventDefault();
    /* Act on the event */
    $(content3).val("");
    editor4.txt.html("", false);
});
</script>
';
?>

<style>
.span_position{
    display:inline;
    background:red;
    border-radius:50%;
    width: 8px;
    height: 8px;
    position: absolute;
    top: 8px;
    left: 28px;
    z-index: 999;
}


span.master-check-nums{
    margin-left: 5px;
}

.navi ul.nav li li a {
    padding-left: 35px;
}
.navi ul.nav li a {
    padding: 10px 5px;
}
</style>

<script>
var toastrPath = '<?php echo $cdnpublic ?>toastr.js/latest/toastr.min.js';
$(document).ready(function () {
    var toastrObj = null;
    if (undefined == typeof toastr || 'object' !== typeof toastr) {
        $.getScript(toastrPath, function (script, textStatus, jqXHR) {
            if (textStatus =='success') {
                toastrObj = toastr;
            }
        });
    }

    var masterShopInterval =null;

    function setWorkTips(parentEl,  navEl, idname, nums){
        var $NavParent = $(parentEl);
        var $NavBox = $(navEl);
        if (nums > 0 && $NavParent.length >=1) {
            if ($NavParent.find(".span_position").length==0) {
                $NavParent.append('<span class="span_position"></span>');
            }
            var $navNums = $NavBox.find('#' + idname);
            if ($navNums.length >0 ) {
                $navNums.html(nums);
            }
            else{
                $NavBox.append('<span class="master-check-nums btn btn-warning btn-xs" id="' + idname + '">' + nums +'</span>');
            }
        }
        else{
            if ($NavParent.length >=1 && $NavParent.find(".span_position").length>0) {
                $NavParent.find(".span_position").remove();
            }
        }
    }

    var getMasterShopNums = function () {
        $.ajax({
            type: "post",
            url: "ajax.php?act=getMasterShopNums",
            data: "data",
            dataType: "json",
            success: function (res) {
                if (res.code == 0) {
                    // 工单提醒
                    setWorkTips('#a_3', '#a_3_4','work-nums', res.data.work_nums);
                    // 订单提醒
                    setWorkTips('#li_2', '#a_2', 'order-nums', res.data.order_nums);
                    // 供货商提现
                    setWorkTips('#a_38', '#a_38_44', 'master-tixian-nums', res.data.master_tixian_nums);
                    // 分站提现
                    setWorkTips('#a_30', '#a_30_37', 'tixian-nums', res.data.tixian_nums);
                    // 库存告急
                    setWorkTips('#li_94', '#a_94','stock-nums',res.data.stock_nums);
                    // 网盘投诉
                    setWorkTips('#li_95', '#a_95','pro_link_nums',res.data.pro_link_nums);
                    // 引流举报
                    setWorkTips('#li_96', '#a_96','pro_report_nums',res.data.pro_report_nums);
                    // 供货商品
                    setWorkTips('#a_38', '#a_38_41','master_check_nums',res.data.master_check_nums);
                    // 商品投诉
                    setWorkTips('#li_97', '#a_97','pro_complaint_nums',res.data.pro_complaint_nums);
                }
            }
        });
    }
    // 120秒查询一次
    setInterval(() => {
        getMasterShopNums();
    }, 120 * 1000);

    // 立即执行一次
    getMasterShopNums();

    window.addEventListener('unload', function (event) {
        console.log('定时器已清理');
        // 执行清理工作
        clearInterval(masterShopInterval);
        masterShopInterval = null;
        // 注意：这里不能执行异步操作或显示对话框
    });
});
</script>
</body>
</html>
