<?php
if (version_compare(PHP_VERSION, '7.0.0', '<')) {
    die('当前服务器环境php版本小于7.0, 请使用7.0~8.0的版本以保障稳定性');
}
/**
 * 斑马自助下单系统
 * 系统后台首页
 **/
$noloadFile = true;
include "../includes/common.php";
if ($isLogin != 1) {
    exit("<script language='javascript'>window.location.href='./login.php';</script>");
}
$title = '后台首页';
$mysqlversion = $DB->count("select VERSION()");
if (!function_exists('checkSec')) {
    exit('系统文件缺失或被损坏，请检测运行环境是否正常，如正常可下载更新包覆盖或重新安装');
}
$sec_msg = checkSec();
$style = '';
if ($is_mb == true) {
    $style = 'padding:10px 0 0 0;margin:0;';
} else {
    $style = 'padding-top:10px;';
}
function unicodeEncode($strLong)
{
    $strArr     = preg_split('/(?<!^)(?!$)/u', $strLong);
    $resUnicode = '';
    foreach ($strArr as $str) {
        $bin_str = '';
        $arr     = is_array($str) ? $str : str_split($str);
        foreach ($arr as $value) {
            $bin_str .= decbin(ord($value));
        }
        $bin_str = preg_replace('/^.{4}(.{4}).{2}(.{6}).{2}(.{6})$/', '$1$2$3', $bin_str);
        $unicode = dechex(bindec($bin_str));
        $_sup    = '';
        for ($i = 0; $i < 4 - strlen($unicode); $i++) {
            $_sup .= '0'; 
        }
        $str = '\\u' . $_sup . $unicode;
        $resUnicode .= $str;
    }
    return $resUnicode;
}
$mysqlInfo = $DB->query("select VERSION()")->fetch();
include './head.php';
?>
<style href="../assets/public/animate.css/3.5.2/animate.min.css"></style>
<style type="text/css">
.GfArticle{
	display: flex;
	border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    background: #fff;
    padding: .7rem;
    margin-bottom: 10px;
}

.GfArticle .title{
	display: inline-block;
	line-height: 25px;
	height: 25px;
}
.GfArticle .content{
	flex: 1;
    display: flex;
    font-size: 1.3rem;
    line-height: 25px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-left: .1rem;
}

.GfArticle .content .article-content{
	flex: 1;
    line-height: 25px;
    height: 155px;
    min-height: 155px;
}
.GfArticle .content .article-content:hover{
    min-height: 460px;
}
.GfArticle .content .slide{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-left: 5px;
}
.GfArticle .content ul{
    padding: 2px 2px;
}

.GfArticle .content li{
	text-align: left;
    padding: 5px 2px;
    font-size: 1.25rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.GfArticle .content li{
	list-style-type: none;
}
.GfArticle .content li:hover,
.GfArticle .content li:active,
.GfArticle .content li:focus
{
	list-style-type: none;
    margin-left: 3px;
}

.GfArticle .content .more {
	flex: 0.5;
}
.GfArticle .content .more a{
	padding: 3px 6px;
    background-color: #2196F3;
    color: #fff;
    border-radius: 0px;
}
.notice .content{
	padding: 5px;
}
.notice .notice-title{
	padding: 12px 2px;
	margin: auto;
	text-align: center;
}
.notice .notice-title h3{
	font-size: 1.85rem;
    margin: 6px 5px;
}
.notice .notice-content img{
	max-width: 100%;
}
.themed-background-success1 {
    background-color: #4DBAC2;
}
.themed-background-success2 {
    background-color: #A7857D;
}
</style>
<div class="modal fade" align="left" id="Nginx-safe" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"><div class="modal-dialog"><div class="modal-content"><div class="modal-header">
    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button><h4 class="modal-title" id="myModalLabel">Nginx服务器静态资源目录assets文件上传漏洞修复方法</h4>
      </div><div class="modal-body"><H4 style="color:red"># 宝塔-Nginx版本</H4><pre>
<code>
	#宝塔安全规则 By 斑马

	#禁止在静态目录运行动态文件
	location ~ ^/assets/(.*)\.(php|php3|php5|asp)
	{
		return 403;
	}

    #禁止下载压缩包
	location ~ (.*)\.(zip|rar|7z|tar|gz)
	{
		return 403;
	}

	#宝塔静态资源目录安全规则 end
</code>
</pre>
将以上代码，复制到宝塔面板->网站->您的网站->设置->配置文件下方类似的代码之前<br>
其中的php-70.conf会根据php版本显示，例如版本是5.6的会显示php-56.conf;
    <pre>
<code>
	#PHP-INFO-START  PHP引用配置，可以注释或修改
	include enable-php-70.conf;
	#PHP-INFO-END
</code>
</pre>

 <H4 style="color:red"># 以下是PHP版本为7.0版本的修改结果示例</H4>
<pre>
<code>
	#ERROR-PAGE-START  错误页配置，可以注释、删除或修改
	error_page 404 /404.html;
	error_page 502 /502.html;
	#ERROR-PAGE-END

	location ~ ^/assets/(.*)\.(php|php3|php5|asp)(.*)$
	{
   		return 403;
	}

	#PHP-INFO-START  PHP引用配置，可以注释或修改
	include enable-php-70.conf;
	#PHP-INFO-END

	#REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
</code>
</pre>
<br>
</div>
<?php
$lasttime         = time() - 86400;
$notify_stock_num = intval($conf['notify_stock_num']) > 0 ? intval($conf['notify_stock_num']) : 0;
if (isset($_GET['zid']) && $_GET['zid'] > 0) {
    $sql = " `active`=1 AND `condition` = 1 and `close`=0 AND `stock`<={$notify_stock_num} AND `stock_time`>={$lasttime} AND `zid`=" . input('zid');
    $link = '&zid=' . input('zid');
} elseif (isset($_GET['kw']) && $_GET['kw']) {
    $kw = input('kw');
    $sql  = " `active`=1 AND `condition` = 1 and `close`=0 AND  (name LIKE '%{$kw}%' OR `zid`={$kw})  AND `stock`<={$notify_stock_num} `stock_time`>={$lasttime}";
    $link = '&zid=' . input('zid');
} else {
    $sql = " `active`=1 AND `condition` = 1 and `close`=0  AND `stock`<={$notify_stock_num} AND  `stock_time`>={$lasttime}";
}
$sql .= " AND `is_curl`!=2";
$numrows  = $DB->count("SELECT count(*) from `cmy_tools` WHERE {$sql}");
?>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!--modal end-->
<!--官方公告modal-->
<!--官方公告modal-->
<div class="modal fade" align="left" id="notice" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
        <h4 class="modal-title">官方公告</h4>
      </div>
      <div class="modal-body notice">
      	   <div class="notice-title">
      	   		<h3 id="notice-title"></h3>
		   </div>
		   <div class="notice-content" id="notice-content">

		   </div>
	  </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<!--官方公告modal end-->
<!-- <div class="col-md-12 center-block" style="float: none;<?php echo $style ?>">
	<?php hook('admin_index_header');?>
	<?php if ($webConfig['test'] != true && $GfArticleList != ''): ?>
	<div class="col-sm-12 col-lg-12 animated fadeInDown">
		<div class="GfArticle">
			<div class="title">
				<img src="../assets/img/tznew.png">
			</div>
		    <div class="content">
		    	<div class="swiper-container article-content">
			        <div class="swiper-wrapper">
			            <?php //echo $GfArticleList; ?>
			        </div>
			    </div>
			    <div class="more">
			        <a href="<?php echo $GfArticleArr['url'] ?>">更多</a>
			    </div>
		    </div>
		</div>
	</div>
	<?php endif;?> -->
<div class="row" style="margin-right: -15px; margin-left: -15px;">
<!--左侧部分-->
<div class="col-xs-12 col-sm-12 col-lg-12">
	<div class="row">
		<div class="col-sm-12 col-sm-6 col-lg-4 animated fadeInLeft">
			<a href="javascript:void(0)" class="widget">
			<div class="widget-content widget-content-mini text-right clearfix">
				<div class="widget-icon pull-left themed-background">
					<i class="fa fa-area-chart text-light-op"></i>
				</div>
				<div class="text-muted-right" style="font-size: 16px;">
				<span class="text-muted-left" style="color: green">订单总数</span>&nbsp;&nbsp;<span id="count1">0</span>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: blue" data-href="list.php">今日订单</span>&nbsp;&nbsp;<span id="todayOrders">0</span>&nbsp;&nbsp;个<br>
				<span class="text-muted-left" style="color: #4B0082">昨日订单</span>&nbsp;&nbsp;<span id="lastOrder">0</span>&nbsp;&nbsp;个
				</div>
			</div>
			</a>
		</div>
		<div class="col-xs-12 col-sm-6 col-lg-4 animated fadeInLeft">
			<a href="javascript:void(0)" class="widget">
			<div class="widget-content widget-content-mini text-right clearfix">
				<div class="widget-icon pull-left themed-background-danger">
					<i class="fa fa-rmb text-light-op"></i>
				</div>
				<div class="text-muted-right" style="font-size: 16px;">
				<span class="text-muted-left" style="color: green">今日交易额</span>&nbsp;&nbsp;<span id="todayMoney">0</span>&nbsp;&nbsp;元<br>
				<span class="text-muted-left" style="color: blue">今日利润额</span>&nbsp;&nbsp;<span id="todayProfit">0</span>&nbsp;&nbsp;元<br>
				<span class="text-muted-left" style="color: #4B0082">今日利润比</span>&nbsp;&nbsp;<span id="todayRatio">0</span>&nbsp;&nbsp;%</span>
				</div>
			</div>
			</a>
		</div>
		<div class="col-xs-12 col-sm-6 col-lg-4 animated fadeInLeft">
			<a href="javascript:void(0)" class="widget">
			<div class="widget-content widget-content-mini text-right clearfix">
				<div class="widget-icon pull-left themed-background-success1">
					<i class="fa fa-database text-light-op"></i>
				</div>
				<div class="text-muted-right" style="font-size: 16px;">
                <span class="clickable-link text-muted-left" style="color: green; cursor: pointer;" data-href="shoplist.php">全站商品</span>&nbsp;&nbsp;<span id="countzs">0</span>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: blue;" data-href="shoplist.php?kw=&cid=-1&active=1&is_curl=-1&close=-1">上架中</span>&nbsp;&nbsp;<span id="sjsp">0</span>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: red;" data-href="shoplist.php?kw=&cid=-1&active=0&is_curl=-1&close=-1">已下架</span>&nbsp;&nbsp;<span id="xjsp">0</span>&nbsp;&nbsp;个</span>
				</div>
			</div>
			</a>
		</div>
		<div class="col-xs-12 col-sm-6 col-lg-4 animated fadeInLeft">
			<a href="javascript:void(0)" class="widget">
			<div class="widget-content widget-content-mini text-right clearfix">
				<div class="widget-icon pull-left themed-background-success">
					<i class="fa fa-shopping-bag text-light-op"></i>
				</div>
				<div class="text-muted-right" style="font-size: 16px;">
				<span class="clickable-link text-muted-left" style="color: green; cursor: pointer;" data-href="master.user.php">供货商户</span>&nbsp;&nbsp;<span id="countghs">0</span>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: blue; cursor: pointer;" data-href="master.shop.php">供货商品</span>&nbsp;&nbsp;<span id="count_ghsp">0</span>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: red; cursor: pointer;" data-href="master.check.php">待审核商品</span>&nbsp;&nbsp;<span id="count_ghsh">0</span>&nbsp;&nbsp;个</span>
				</div>
			</div>
			</a>
		</div>
			<div class="col-xs-12 col-sm-6 col-lg-4 animated fadeInLeft">
    <a href="javascript:void(0)" class="widget">
        <div class="widget-content widget-content-mini text-right clearfix">
            <div class="widget-icon pull-left themed-background-warning">
                <i class="fa fa-refresh text-light-op"></i>
            </div>
            <div class="text-muted-right" style="font-size: 16px;">
                <span class="clickable-link text-muted-left" style="color: blue; cursor: pointer;" data-href="list.php?kw=&type=0&status=0">待处理订单</span>&nbsp;&nbsp;<span id="count3">0</span>&nbsp;&nbsp;个<br>
                <span class="clickable-link text-muted-left" style="color: #FF1493; cursor: pointer;" data-href="list.php?kw=&type=0&status=2">处理中订单</span>&nbsp;&nbsp;<span id="clzorder">0</span>&nbsp;&nbsp;个<br>
                <span class="clickable-link text-muted-left" style="color: red; cursor: pointer;" data-href="list.php?kw=&type=0&status=3">异常中订单</span>&nbsp;&nbsp;<span id="count15">0</span>&nbsp;&nbsp;个
            </div>
        </div>
    </a>
</div>
		<div class="col-xs-12 col-sm-6 col-lg-4 animated fadeInLeft">
			<a href="javascript:void(0)" class="widget">
			<div class="widget-content widget-content-mini text-right clearfix">
				<div class="widget-icon pull-left themed-background-success2">
					<i class="fa fa-spinner text-light-op"></i>
				</div>
				<div class="text-muted-right" style="font-size: 16px;">
				<span class="clickable-link text-muted-left" style="color: blue; cursor: pointer;" data-href="workorder.php">投诉工单</span>&nbsp;&nbsp;<span id="count_clgd">0</span>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: #FF1493; cursor: pointer;" data-href="shopstock.php">库存告急</span>&nbsp;&nbsp;<?php echo $numrows; ?>&nbsp;&nbsp;个<br>
				<span class="clickable-link text-muted-left" style="color: red; cursor: pointer;" data-href="">网盘失效</span>&nbsp;&nbsp;<span id="wpts">0</span>&nbsp;&nbsp;个
				</div>
			</div>
			</a>
		</div>
	</div>
		</div>
		<div class="col-xs-12 col-md-8 col-lg-8 animated fadeInLeft">
	<div class="row">
		<!--站点数据统计-->
		<div class="col-xs-12 col-md-6 col-lg-6">
				<div class="widget">
					<div class="widget-content border-bottom">
						<span class="pull-right text-muted"><i class="fa fa-circle"></i></span>
					分站代理统计
					</div>
					<div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-sitemap text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;全站代理总数</span><br><br>
								<center><a href="sitelist.php" style="color: #800080; text-decoration: none;"><span id="count6"></span></a>&nbsp;个</font></center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cloud text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日新增分站</span><br><br>
								<center><a href="sitelist.php" style="color: #800080; text-decoration: none;"><span id="count7"></span></a>&nbsp;个</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">

								<h4 class="widget-heading"><i class="fa fa-recycle push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日新增用户</span><br><br>

								<center><a href="userlist.php" style="color: #800080; text-decoration: none;"><span id="daili_user"></span></a>&nbsp;个</center></h4>
							</div>
						</div>
					</div>
					<?php if ($conf['data_type'] == 2) {?>
					<div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-amex text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;分站累计提现</span><br><br>
								<center><span id="countdl"></span>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-visa text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日分站提成</span><br><br>
								<center><a href="record.php" style="color: #FF1493; text-decoration: none;"><span id="count8"></span></a>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-mastercard text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;分站待处理提现</span><br><br>
								<center><a href="tixian.php" style="color: red; text-decoration: none;">
                <span id="count11"></span>
            </a>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
					<div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-4 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-briefcase text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;全站代理余额</span><br><br>
								<center><span id="daili_money"></span>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-cart-arrow-down text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日分站消费</span><br><br>
								<center><a href="record.php" style="color: blue; text-decoration: none;"><span id="fz_xf"></span></a>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-plus-square text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日分站充值</span><br><br>
								<center><a href="record.php" style="color: blue; text-decoration: none;"><span id="fz_cz"></span></a>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
				<?php } else {?>
			          <div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-6 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-visa text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日分站提成</span><br><br>
								<center><a href="record.php" style="color: #FF1493; text-decoration: none;"><span id="count8"></span></a>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-6 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-mastercard text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;分站待处理提现</span><br><br>
								<center><a href="tixian.php" style="color: red; text-decoration: none;">
                <span id="count11"></span>
            </a>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
					<div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-6 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-cart-arrow-down text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日分站消费</span><br><br>
								<center><a href="record.php" style="color: blue; text-decoration: none;"><span id="fz_xf"></span></a>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-6 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-plus-square text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日分站充值</span><br><br>
								<center><a href="record.php" style="color: blue; text-decoration: none;"><span id="fz_cz"></span></a>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
				<?php }?>
				</div>
		</div>
		<!--站点数据统计 end-->
		<!--站点支付流水统计-->
		<div class="col-xs-12 col-md-6 col-lg-6">
				<div class="widget">
					<div class="widget-content border-bottom">
						<span class="pull-right text-muted"><i class="fa fa-circle"></i></span>
					综合交易统计
					</div>
					<div class="widget-content widget-content-full">
						<div class="row text-center">
<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-amex text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;供货累计提现</span>
								<br><br>
								<center><span id="countgh"></span>&nbsp;元</center></h4>
							</div>
<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-visa text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日供货提成</span>
								<br><br>
								<center><a href="master.record.php" style="color: #FF1493; text-decoration: none;"><span id="count22"></span></a>&nbsp;元</center></h4>
							</div>
<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-mastercard text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;供货待处理提现</span>
								<br><br>
								<center><a href="master.tixian.php" style="color: red; text-decoration: none;">
                <span id="count21"></span>
            </a>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
<div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-qq text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日QQ钱包</span>
								<br><br>
								<center><span id="todayqqpay"></span>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-wechat text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日微信支付</span>
								<br><br>
								<center><span id="todaywxpay"></span>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right border-bottom">
								<h4 class="widget-heading"><i class="fa fa-cc-paypal text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;今日支付宝</span>
								<br><br>
								<center><span id="todayalipay"></span>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
<div class="widget-content widget-content-full">
						<div class="row text-center">
							<div class="col-xs-4 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-qq text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;昨日QQ钱包</span>
								<br><br>
								<center><span id="lastqqpay"></span>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-wechat text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;昨日微信支付</span>
								<br><br>
								<center><span id="lastwxpay"></span>&nbsp;元</center></h4>
							</div>
							<div class="col-xs-4 push-inner-top-bottom border-right">
								<h4 class="widget-heading"><i class="fa fa-cc-paypal text-dark push text-cmgg-md"></i><br>
								<span class="text-tips text-cmgg-xs">&nbsp;昨日支付宝</span>
								<br><br>
								<center><span id="lastalipay"></span>&nbsp;元</center></h4>
							</div>
						</div>
					</div>
				</div>
		</div>
		<!--站点支付流水统计 end-->
	</div>
		<script>
    document.addEventListener('DOMContentLoaded', function () {
        const links = document.querySelectorAll('.clickable-link');
        links.forEach(function (link) {
            link.addEventListener('click', function () {
                const href = this.getAttribute('data-href');
                if (href) {
                    window.location.href = href;
                } else {
                    console.error('跳转链接不存在');
                }
            });
        });
    });
</script>
   <!--图表统计-->
	<div class="row">
		<div class="col-sm-12 col-md-12 col-lg-12">
			<div class="widget">
				<div class="widget-content border-bottom">
		          <span id="tjTitle">一周交易金额、订单数量、注册用户、充值金额统计</span>
		          <div class="" style="float: right !important;">
		          	  <span onclick="getPointData('week')" class="btn btn-success btn-xs">近一周</span>
			          <span onclick="getPointData('month')" class="btn btn-primary btn-xs">近一月</span>
			          <span onclick="getPointData('month3')" class="btn btn-warning btn-xs">近三月</span>
		          </div>
				</div>
				<div class="widget-content border-bottom themed-background-muted">

					<div id="chart-container" style="height: 393px;width: 100%">

					</div>

				</div>

			</div>
		</div>
	</div>
	<!--图表统计 end-->
</div>
<!--左侧部分 end-->
<!--右侧部分 -->
      <div class="col-xs-12 col-md-6 col-lg-4">

	<!-- <div class="widget">

		<div class="widget-content border-bottom text-dark">

		<span class="pull-right text-muted">
			<a href="javascript:;">更多</a>
			</span>
			官方公告
		</div>
		<div class="GfArticle animated fadeIn">
			<div class="content">
				 <ul class="list-group text-dark swiper-container article-content" id="article-content">
					<li class="swiper-slide slide">
		                <a class="" href="javascript:;" data-jump="" data-content="" data-title="">正在获取中，请稍后</a>
		            </li>
				</ul>
			</div>
		</div>
	</div> -->
	<div class="widget">

	<div class="widget-content border-bottom">

	<span class="pull-right text-muted"><i class="fa fa-check"></i></span>

	安全中心

	</div>

		<ul class="list-group">

	<?php

foreach ($sec_msg as $row) {

    echo $row;

}

if (count($sec_msg) == 0) {
    echo '<li class="list-group-item"><span class="btn-sm btn-success">正常</span>&nbsp;暂未发现网站安全问题</li>';
}

?>

		</ul>

	</div>
	<div class="widget">

		<div class="widget-content border-bottom text-dark">

		<span class="pull-right text-muted"><i class="fa fa-check-square"></i></span>
		检测更新
		</div>
			<ul class="list-group text-dark" id="checkupdate">
				<li class="list-group-item">正在获取中</li>
			</ul>
	</div>
	<!-- 程序信息 -->
    <div class="widget">
		<div class="widget-content border-bottom">
			<span class="pull-right text-muted"><i class="fa fa-circle"></i></span>
		程序信息
		</div>
		<div class="panel panel-body">
			<table class="table">
			 <thead>
			 	<tr><th>项目</th><th>详情</th><th>操作</th></tr>
			 </thead>
			 <tbody>
			 	    <tr><td>授权站节点</td>
			 	    	<td>
			 	    	<?php echo getAuthServerName(true); ?>
				 	    </td>
				 	    <td>
				 	    <a href="javascript:layer.msg('退出后台登录后重新选择即可');">切换</a>
				 	    </td>
				 	</tr><tr><td>程序名称</td><td>斑马云商城Plus</td><td>-</td></tr>
			 		<tr><td>程序版本</td><td>V<?php echo $SYSVERSION ?></td><td>-</td></tr>
			 		<tr><td>程序版号</td><td>Build <?php echo $conf['version'] ?> 独立版</td><td>-</td></tr>
			 		<tr><td>数据库版本</td><td><?php echo $conf['SQLVERSION'] ?></td><td>-</td></tr>
			 		<tr>
			 			<td>PHP版本</td>
			 			<td>
          				<?php echo phpversion();if (ini_get('safe_mode')) {echo '线程安全';} else {echo '非线程安全';} ?>

          				</td>
          				<td>
          				-
          				</td>
          			</tr>
			 		<tr><td>Mysql版本</td><td><?php echo $mysqlInfo[0] ?></td><td>-</td></tr>
			 		<tr><td>服务器类型</td><td><?php echo PHP_OS . " & " . $_SERVER['SERVER_SOFTWARE'] ?></td><td>-</td></tr>
			 		<tr><td>单次上传限制</td><td><?php echo ini_get('upload_max_filesize') ?></td><td>-</td></tr>
			 </tbody>
		</table>
		</div>
	</div>
    <!-- / 程序信息 -->
</div>
<!--右侧部分 end-->
</div>


  </div>

<script type="text/javascript" src="<?php echo $cdnpublic ?>echarts/4.4.0-rc.1/echarts.min.js"></script>
<script type="text/javascript" src="<?php echo $cdnpublic ?>toastr.js/latest/toastr.min.js"></script>
<script>
var utf8to16 = function (str) {
    var out, i, len, c;
    var char2, char3;
    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
        c = str.charCodeAt(i++);
        switch (c >> 4) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
                out += str.charAt(i - 1);
                break;
            case 12:
            case 13:
                char2 = str.charCodeAt(i++);
                out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                break;
            case 14:
                char2 = str.charCodeAt(i++);
                char3 = str.charCodeAt(i++);
                out += String.fromCharCode(((c & 0x0F) << 12) | ((char2 & 0x3F) << 6) | ((char3 & 0x3F) << 0));
                break
        }
    }
    return out
};
$(document).on('click', '.read-notice', function(event) {
	event.preventDefault();
	/* Act on the event */
	var content = $(this).data('content');
	var title = $(this).data('title');
	var jump = $(this).data('jump');
	var href = $(this).attr('href');
	if (jump==1 && 'string'===typeof href && href.indexOf('http')>=0) {
		window.open(href);
	}
	else {
		$("#notice-content").html(utf8to16(window.atob(content)));
		$("#notice-title").html(title);
		$("#notice").modal('show');
	}
});
$(document).ready(function(){
    var index = layer.load(0);
	$.ajax({
		type : "GET",

		url : "ajax.php?act=getData&noloadFile=1",

		dataType : 'json',

		async: true,

		success : function(data) {
			layer.close(index);

			$('#title').html('后台管理首页');

			$('#yxts').html(data.yxts);

			$('#count1').html(data.count1);//订单总数

			$('#count3').html(data.count3);//待处理

			$('#count6').html(data.count6);//代理总数

			$('#count7').html(data.count7);//新增分站

			$('#count8').html(data.count8);//今日提成

			$('#count11').html(data.count11);//待处理提现

			$('#count15').html(data.count15);//异常订单
			
			$('#count21').html(data.count21);
			
			$('#count22').html(data.count22);

			$('#clzorder').html(data.clzorder);

			$('#lastOrder').html(data.lastdata.orders);

			$('#lastMoney').html(data.lastdata.money);

			$('#lastProfit').html(data.lastdata.profit);

			$('#lastBili').html(data.lastdata.ratio);

			$('#lastqqpay').html(data.lastdata.qqpay);

			$('#lastwxpay').html(data.lastdata.wxpay);

			$('#lastalipay').html(data.lastdata.alipay);

			$('#todayqqpay').html(data.todaydata.qqpay);

			$('#todaywxpay').html(data.todaydata.wxpay);

			$('#todayalipay').html(data.todaydata.alipay);

			$('#todayOrders').html(data.todaydata.orders);

			$('#todayProfit').html(data.todaydata.profit);

			$('#todayRatio').html(data.todaydata.ratio);

			$('#todayMoney').html(data.todaydata.money);

			$('#fz_xf').html(data.fz_xf);

			$('#fz_cz').html(data.fz_cz);

			$('#daili_user').html(data.daili_user);

			$('#daili_money').html(data.daili_money);

			$('#daili_point').html(data.daili_point);
			
			$('#gonghuo_point').html(data.gonghuo_point);
			
			$('#countgh').html(data.countgh);//供货累计提现
			
			$('#countdl').html(data.countdl);//分站累计提现
			
			$('#countzs').html(data.countzs);//全站商品
			
			$('#countghs').html(data.countghs);//供货商户
			
			$('#count_ghsh').html(data.count_ghsh);//供货审核
			
			$('#count_ghsp').html(data.count_ghsp);//供货商品
			
			$('#count_clgd').html(data.count_clgd);//投诉工单
			
			$('#wpts').html(data.wpts);//网盘失效
			
			$('#yljb').html(data.yljb);//网盘失效
			
            $('#sjsp').html(data.sjsp);//上架商品
            
            $('#xjsp').html(data.xjsp);//下架商品
            
			setTimeout(function(){
				getPointData('week');
			}, 500);
			setTimeout(function(){
				getWorkData();
			}, 1500);
			setTimeout(function(){
				checkupdate();
				// getArticleList();
			}, 2500);
		},
		error: function () {
			layer.close(index);
			layer.alert("服务器出现卡顿或异常请求超时，请稍后刷新重试！");
		}

	});
})


function showToastr(msg, type, callback){
    toastr.options = {
        "tapToDismiss":false,
        "closeButton": true,
        "debug": false,
        "positionClass": "toast-bottom-right",
        "showDuration": "300",
        "hideDuration": "10000",
        "timeOut": "5000",
        "extendedTimeOut": "10000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "onclick": callback
	};

	if (type=='info') {
		toastr.info(msg);
	}
	else if (type=='warning') {
		toastr.warning(msg);
	}
	else if (type=='error') {
		toastr.error(msg);
	}
	else {
		toastr.success(msg);
	}
}

function getPointData(type){
   if (type=="month") {
    var $type='2';
    var title="一月统计";
   }
   else if (type=="month3") {
   	var $type='3';
   	var title="三月统计";
   }
   else{
   	var $type='1';
   	var title="一周统计";
   }
    $.ajax({
		type : "GET",
		url : "ajax.php?act=getPointData&noloadFile=1&type="+$type,
		dataType : 'json',
		success : function(data) {
			if(data.code==0){
				$("#tjTitle").html(title);
				chartUpdata(data);
			}else{
			    layer.alert(data.msg);
			}
		},
		error:function(e){
			layer.close(ii);
			console.log(e);
			layer.alert('服务器错误，请稍后再试！');
		}
	});

}

var getWorkData = function (){
   	$.ajax({
		type : "GET",
		url : "ajax.php?act=getWorkData",
		dataType : 'json',
		success : function(data) {
			if(data.code==0){
				if (data.data.new_reply == true) {
					showToastr('工单系统有新的回复！', 'succ', function (){
                        console.log('Toastr被点击');
                    });
				}
				else if (data.data.dcl >0 ) {
					showToastr('还有'+data.data.dcl+'个工单未处理，请及时查看', 'error', function () {
                        console.log('Toastr被点击');
                    });
				}

				if (data.data.open===1) {
					if (data.data.speed<=0) {
						data.data.speed = 5;
					}
					setTimeout(getWorkData, data.data.speed * 1000);
				}

				if (!!data.data.online) {
					showToastr('<b>在线用户：预计' + data.data.online + '人</b>', 'success', function (){
                        console.log('Toastr被点击');
                    });
				}

                if (data.data.kucunlist && 'object'=== typeof data.data.kucunlist) {
                    if (data.data.kucunlist.length > 10) {
                        showToastr('提示: 当前有' + data.data.kucunlist.length + '个商品的库存小于等于' + data.data.notify_stock_num+ '个, 请处理</b>', 'error', function (){
                            console.log('Toastr被点击');
                        });
                    }
                    else{
                        $.each(data.data.kucunlist, function (indexInArray, row) {
                            if (indexInArray <5 ) {
                                showToastr('提示: 商品<b>' + row.name + '的库存仅剩' + row.stock+ '个</b>|' + (row.is_curl == 4 ?'点击加卡':'点击改库存'), 'error', function (){
                                    console.log('Toastr被点击');
                                    if (row.is_curl ==4) {
                                        window.location.href='./fakakms.php?my=add&tid=' + row.tid
                                    }else{
                                        window.location.href='./shopedit.php?my=edit&tid=' + row.tid
                                    }
                                });
                            }
                        });
                    }
				}
			}else{
			    layer.alert(data.msg);
			}
		},
		error:function(e){
			console.log(e);
			layer.alert('服务器错误，请稍后再试！');
		}
	});
}

// var getArticleList = function (){
// 	var error = '<li class="swiper-slide slide"><a class="" href="javascript:;" data-jump="" data-content="" data-title="">获取公告结果异常，请稍后再试</a></li>';
//    	$.ajax({
// 		type : "GET",
// 		url : "ajax.php?act=getArticleList",
// 		dataType : 'json',
// 		success : function(data) {
// 			if(data.code == 0){
// 				$("#article-content").html(data.data);
// 			}
// 		},
// 		error:function(e){
// 			console.log(e);
// 			$("#article-content").html(error);
// 			//layer.alert('服务器错误，请稍后再试！');
// 		}
// 	});
// }

var checkupdate = function () {
	$.ajax({
		type : "GET",
		url : "ajax.php?act=checkupdate",
		dataType : 'json',
		success : function(data) {
			if(data.code == 0){
				$("#checkupdate").html('');
				$("#checkupdate").append(data.data.msg);
				if (data.data.notice) {
					$("#checkupdate").append(data.data.notice);
				}
			}
		},
		error:function(e){
			console.log(e);
			layer.alert('服务器错误，请稍后再试！');
		}
	});
}

//统计图表 可多种统计数据 By 斑马

var chartUpdata = function(data){
	if (data.chart) {
		var data= data.chart;
	}
	else{
		var data= data.data;
	}

	// $('#canvas').remove();
    // $('#chart-container').html('<canvas id="canvas" style="heigh:393px"></canvas>'); 旧版统计插件 已弃用
     var myChart = echarts.init(document.getElementById('chart-container'));
     var option = {
            title: {
                text: '<?php $is_mb === true ? '' : '运营统计数据'?>'
            },
             tooltip : {
		        trigger: 'axis',
		        axisPointer: {
		            type: 'cross',
		            label: {
		                backgroundColor: '#6a7985'
		            }
		        }
		    },
            legend: {
                data:['交易金额','订单数量','注册用户','充值金额']
            },
            xAxis: {
                data: data.date
            },
            grid: {
		        left: '1%',
		        right: '1%',
		        bottom: '1%',
		        containLabel: true
		    },
            yAxis: {},
            series: [{
			            name:'交易金额',
			            type:'line',
			            areaStyle: {},
			            label: {normal:{
		                    show: true,
		                    position: 'top'
		                    }
			            },
			            data:data.money
			        },
			        {
			            name:'订单数量',
			            type:'line',
			            areaStyle: {},
			            label: {normal:{
		                    show: true,
		                    position: 'top'
		                    }
			            },
			            data:data.orders
			        },
			        {
			            name:'注册用户',
			            type:'line',
			            areaStyle: {},
			            data:data.users
			        },
			        {
			            name:'充值金额',
			            type:'line',
			            areaStyle: {normal: {}},
			            label: {normal:{
		                    show: true,
		                    position: 'bottom'
		                    }
			            },
			            data:data.recharge
			        }]
        };
        myChart.setOption(option);
}
</script>
<script type="text/javascript" src="<?php echo $cdnserver ?>assets/public/Swiper/4.5.0/js/swiper.min.js?<?php echo $jsver ?>"></script>
<script type="text/javascript">
new Swiper('.article-content', {
	direction: 'vertical',
	loop: true,
	autoplay: {
	    delay: 6000,
	    stopOnLastSlide: false,
	    disableOnInteraction: false,
	},
})
</script>
<?php hook('admin_index_footer');?>

<?php include 'footer.php';?>