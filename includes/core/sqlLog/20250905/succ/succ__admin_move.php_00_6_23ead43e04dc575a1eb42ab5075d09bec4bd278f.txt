
[2025-09-05 00:58:43] Handle <175700512369817> SQL[SELECT * from `cmy_site` where `siteurl`='154.44.31.85:2628' OR `siteurl2`='154.44.31.85:2628' limit 1]; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[SELECT * FROM `cmy_plugin` where `status`=1]; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(*) from `cmy_config`]; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[SELECT count(*) from cmy_points where 1]; Time[21ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[SELECT count(*) from shua_points]; Time[32ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[SELECT * from `shua_points` limit 40000,10000]; Time[64ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'id']; Time[2ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'zid']; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'action']; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'point']; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'bz']; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'addtime']; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'orderid']; Time[1ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'status']; Time[2ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[SET global max_allowed_packet = *********] Error[SQLSTATE[42000]: Syntax error or access violation: 1227 Access denied; you need (at least one of) the SUPER privilege(s) for this operation]; Time[0ms]
[2025-09-05 00:58:43] Handle <175700512369817> SQL[BEGIN]; Time[1ms]
[2025-09-05 00:58:47] Handle <175700512369817> SQL[COMMIT]; Time[23ms]