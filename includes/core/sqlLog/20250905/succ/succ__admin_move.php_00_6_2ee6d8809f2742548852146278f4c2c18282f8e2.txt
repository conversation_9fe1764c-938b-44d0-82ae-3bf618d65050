
[2025-09-05 00:57:47] Handle <175700506769914> SQL[SELECT * from `cmy_site` where `siteurl`='154.44.31.85:2628' OR `siteurl2`='154.44.31.85:2628' limit 1]; Time[1ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[SELECT * FROM `cmy_plugin` where `status`=1]; Time[1ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(*) from `cmy_config`]; Time[1ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[SELECT count(*) from cmy_points where 1]; Time[0ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[SELECT count(*) from shua_points]; Time[21ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[SELECT * from `shua_points` limit 0,10000]; Time[31ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'id']; Time[2ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'zid']; Time[1ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'action']; Time[2ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'point']; Time[2ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'bz']; Time[1ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'addtime']; Time[2ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'orderid']; Time[2ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[select count(COLUMN_NAME) from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME = 'cmy_points' and COLUMN_NAME = 'status']; Time[2ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[ALTER TABLE `cmy_points` ADD `status` text DEFAULT NULL]; Time[44ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[SET global max_allowed_packet = *********] Error[SQLSTATE[42000]: Syntax error or access violation: 1227 Access denied; you need (at least one of) the SUPER privilege(s) for this operation]; Time[1ms]
[2025-09-05 00:57:47] Handle <175700506769914> SQL[BEGIN]; Time[1ms]
[2025-09-05 00:57:50] Handle <175700506769914> SQL[COMMIT]; Time[27ms]